<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="Promotions" pageSubtitle="Edit Promotion" />

    <div class="block  py-4  mx-3 px-5">
      <!-- 1 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs font-bold text-black block ">Promo Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.promo_name" placeholder="Enter Promo Name">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Filter Promotion</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                  v-model="promo.promotion_type_id">
            <option value="" disabled>Select Promotion</option>
            <option v-for="item in promotion_filters" :value="item.value">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block">Select Start & End Date</label>
          <VueDatePicker
              v-model="date"
              range
              :preset-ranges="presetRanges"
              position="center"
              :clearable="true"
              :enable-time-picker="false"
              @closed="selectDate">
            <template #yearly="{ label, range, presetDateRange }">
              <span @click="presetDateRange(range)">{{ label }}</span>
            </template>
          </VueDatePicker>
        </div>
      </div>

      <!-- 1  Choose image-->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block">
          <label class="text-xs font-bold text-black block ">Promo URL</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.promo_url" placeholder="Enter Promo URL">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block">Promo Image URL</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 name="image" v-model="promo.promo_images" placeholder="Enter Image url" required>
        </div>
      </div>

      <!-- 2 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="block">
          <label class="text-xs font-bold text-black mb-1 block ">Bonus Amount</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.bonus_amount" placeholder="Enter Bonus Amount">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Min Odds</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.min_odds" placeholder="Enter Min Odds">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Min Odds Per Pick</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.min_odds_per_pick" placeholder="Enter Min Odds Per Pick">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Bet Count</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.bet_count" placeholder="Enter Bet Count">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Min Stake</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.min_stake" placeholder="Enter Min Stake">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Min Selections</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.min_selections" placeholder="Enter Min Selections">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Max Times</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.max_times" placeholder="Enter Max Times">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Max Win</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.max_win" placeholder="Enter Max Win">
        </div>
      </div>

      <!-- 3 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs font-bold text-black block ">Frequency</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                  v-model="promo.frequency">
            <option value="" disabled>Select Frequency</option>
            <option v-for="item in frequencyOptions" :value="item.value">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Deduct Stake</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                  v-model="promo.deduct_stake">
            <option value="" disabled>Select</option>
            <option v-for="item in yesNo" :value="item.value">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Restrict Withdrawal</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                  v-model="promo.restrict_withdrawals">
            <option value="" disabled>Select</option>
            <option v-for="item in restrictWithdrawal" :value="item.value">
              {{ item.text }}
            </option>
          </select>

        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Allow Duplicate Events</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                  v-model="promo.allow_duplicate_events">
            <option value="" disabled>Select</option>
            <option v-for="item in yesNo" :value="item.value">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Status</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                  v-model="promo.status">
            <option value="" disabled>Select status</option>
            <option v-for="item in statuses" :value="item.value">
              {{ item.text }}
            </option>
          </select>
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block ">Expiry Period (hrs)</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="promo.expiry_period" placeholder="Enter Expiry Period">
        </div>
      </div>

      <!--      <div class="grid grid-cols-3 gap-4 mb-4">
              <div class="block">
                <label class="text-xs font-bold text-black block ">Market Conditions</label>
                <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                       v-model="promo.market_conditions.text" placeholder="Enter Market Conditions">
              </div>
            </div>-->

      <!-- Buttons -->
      <div class="grid grid-cols-2 gap-4  text-center mt-10">
        <router-link class="block text-xs font-bold px-4 py-2 bg-neutral-100 border rounded-md"
                     :to="{name: 'promotions'}">
          Cancel
        </router-link>

        <button class="block text-xs font-bold px-4 py-2 bg-primary rounded-md" @click="editPromo" id="submit">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>
import {mapActions} from "vuex";
import moment from "moment-timezone";
import VueDatePicker from "@vuepic/vue-datepicker";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    VueDatePicker,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      isLoading: false,
      fullPage: false,
      loading: false,
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      form: {
        timestamp: "254",
        username: '',
        display_name: '',
        role_id: '',
        permissions_acl: [],
      },
      //
      roles: [],
      permissions: [],

      folder: 'images',

      filter: null,
      //
      promo: {
        promo_id: null,
        timestamp: Date.now(),
        promo_name: "",
        promo_url: "",
        promo_details: "",
        promo_images: "",
        promotion_type_id: "",
        starting_date: "",
        ending_date: "",
    
          bonus_amount: 30,
          min_odds: 4.99,
          min_odds_per_pick: 4.99,
          bet_count: 2,
          min_stake: 20,
          min_selections: 5,
          max_times: 1,
          max_win: 10000,
          frequency: "once",
          deduct_stake: 1,
          restrict_withdrawals: 0,
          allow_duplicate_events: 1,
          allow_duplicate_events_per_ip: 1,
          expiry_period: 48,
          market_conditions: [
            {name: "1X2", value: ""},
            {name: "Both Teams To Score", value: "BTTS"}
          ],
          status: 1,
        
      },

      // Filter & filterParams
      promotion_filters: [],
      filterParams: {
        timestamp: '',
        promo_name: '',
        component: '',
        type_id: '',
        status: '',
        limit: '',
        sort: '',
        page: '',
        start: '',
        end: '',
      },
      // Yes/no
      yesNo: [
        {text: 'Yes', value: 1},
        {text: 'No', value: 0}
      ],
      // Frequency Options
      frequencyOptions: [
        {text: 'Once', value: 'once'},
        {text: 'Daily', value: 'daily'},
        {text: 'Weekly', value: 'weekly'},
        {text: 'Bi-Weekly', value: 'bi-weekly'},
        {text: 'Monthly', value: 'monthly'},
      ],
      // statuses
      statuses: [
        {text: 'Active', value: 1},
        {text: 'Deactivated', value: 2},
        {text: 'Suspended', value: 3},
      ],
      // restrict Withdrawal
      restrictWithdrawal: [
        {text: 'Yes', value: 1},
        {text: 'No', value: 0},
      ],
      //
      date: null,
      presetRanges: [
        {label: "All", range: ["", ""]},
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },
  async mounted() {
    await this.selectDate();
    await this.setPromotionFilters();
    this.setForm();
  },
  methods: {
    ...mapActions(["getPromotionFilters", "editPromotion", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async editPromo() {
      let app = this
      let payload = app.promo;
      payload.timestamp = Date.now()

      console.log("Function : ", JSON.stringify(payload));

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this add this file!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.editPromotion(payload)
        },
      })
          .then(async (result) => {
            console.log("Result: ", JSON.stringify(result))
            if (result.value.status === 200) {
              // app.link = result.value.message
              await app.$swal.fire({
                title: 'Added!',
                text: result.value.message,
                icon: 'success',
                confirmButtonColor: '#26a528',
                confirmButtonText: 'OK'
              })
              await app.$router.push({name: 'promotions'})
            } else {
              app.$swal.fire({
                title: 'Error!',
                text: result.value.message,
                icon: 'error',
                confirmButtonColor: '#26a528',
                confirmButtonText: 'OK'
              });
            }
          })
    },

    // Fetch Promotion Filters
    async setPromotionFilters() {
      let app = this
      app.isLoading = true
      app.filterParams.timestamp = Date.now()
      let response = await this.getPromotionFilters(app.filterParams)
      let list = []
      if (response.status === 200) {
        for (let i = 0; i < response.message.result.length; i++) {
          let item = response.message.result[i];
          list.push({text: item.type, value: parseInt(item.id)});
        }
        app.promotion_filters = list
      }
      app.isLoading = false
    },

    //
    async selectDate() {
      if (this.date !== null) {
        this.promo.starting_date = this.formatDate(this.date[0])
        this.promo.ending_date = this.formatDate(this.date[1])
      } else {
        this.promo.starting_date = this.formatDate(Date.now())
        this.promo.ending_date = this.formatDate(Date.now())
      }
    },

    // format date
    formatDate(date) {
      return moment(date).format('YYYY-MM-DD')
    },

    //
    setForm() {
      let app = this;
      let promo = this.$store.state.promo;
      app.promo.promo_id = promo.promo_id;
      app.promo.promo_name = promo.promo_name ?? "";
      app.promo.promo_url = promo.promo_url ?? "";
      app.promo.promo_details = promo.promo_details ?? "";
      app.promo.promo_images = promo.promo_images ?? "";
      app.promo.starting_date = promo.starting_date ?? "";
      app.promo.ending_date = promo.ending_date ?? "";
      app.promo.bonus_amount = promo.bonus_amount ?? "";
      app.promo.min_odds = promo.min_odds ?? "";
      app.promo.min_odds_per_pick = promo.min_odds_per_pick ?? "";
      app.promo.bet_count = promo.bet_count ?? "";
      app.promo.min_stake = promo.min_stake ?? "";
      app.promo.min_selections = promo.min_selections ?? "";
      app.promo.max_times = promo.max_times ?? "";
      app.promo.max_win = promo.max_win ?? "";
      app.promo.frequency = promo.frequency ?? "";
      app.promo.deduct_stake = promo.deduct_stake ?? "";
      app.promo.restrict_withdrawals = promo.restrict_withdrawals ?? "";
      app.promo.allow_duplicate_events = promo.allow_duplicate_events ?? "";
      // app.promo.allow_duplicate_events_per_ip = promo.allow_duplicate_events_per_ip ?? "";
      app.promo.expiry_period = promo.expiry_period ?? "";
      app.promo.status = promo.promo_status ?? "";
      app.promo.promotion_type_id = promo.promo_type_id ?? "";
      // console.log("KKK: " + JSON.stringify(app.promo));
    },
  },
}
</script>
