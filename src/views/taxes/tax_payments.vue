<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="Tax" pageSubtitle="Payments" />

    <div class="block p-3">
      <auto-table
        :headers="tableHeaders"
        :data="filteredTaxPayment"
        :loading="isLoading"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :has-actions="true"
        :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage"
        @items-per-page-change="handleLimitChange"
      >
        <!-- ID Column -->
        <template #id="{ item }">
          <strong>{{ item.id }}</strong>
        </template>

        <!-- PRN Number Column -->
        <template #prn_number="{ item }">
          <span class="font-medium">{{ item.prn_number }}</span>
        </template>

        <!-- PRN Amount Column -->
        <template #prn_amount="{ item }">
          <span class="font-medium">{{ item.prn_amount }}</span>
        </template>

        <!-- PRN Reg Date Column -->
        <template #prn_reg_date="{ item }"> 
          <span class="font-medium">{{ item.prn_reg_date }}</span>
        </template>

        <!-- Tax Type Column -->
        <template #tax_type="{ item }">
          <span class="font-medium">{{ item.tax_type }}</span>
        </template>

        <!-- Response Description Column -->
        <template #response_description="{ item }">
          <strong>{{ item.response_description }}</strong>
        </template>

        <!-- Receipt Number Column -->
        <template #reciept_number="{ item }">
          <strong>{{ item.reciept_number }}</strong>
        </template>

        <!-- Status Column -->
        <template #response_code="{ item }">
          <button v-if="parseInt(item.response_code) === 1"
                  class="inline-block px-4 py-1 rounded-md text-white bg-orange-500">
            UnPaid
          </button>
          <button v-else-if="parseInt(item.response_code) === 0"
                  class="inline-block px-4 py-1 rounded-md text-white bg-green-500">
            Paid
          </button>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <span style="font-size: 11px; color: grey">{{ moment(item.created_at).format('llll') }}</span>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <div class="flex space-x-2 justify-center">
            <button
              @click="toggleDetails(item)"
              class="inline-block px-6 py-2 rounded-md text-white bg-blue-500">
              View
            </button>

            <button v-if="parseInt(item.response_code) === 0"
              @click="payTaxes(item)"
              class="inline-block px-6 py-2 rounded-md text-white bg-green-500">
              Pay
            </button>
          </div>
        </template>
      </auto-table>

      <!--Modals-->
      <!--Blacklist Modal-->
      <div v-if="taxPayment!=null" class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': viewModelOpen, 'opacity-0 pointer-events-none': !viewModelOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">Callback Extra Data</p>
              <div class="modal-close cursor-pointer z-50" @click="viewModelOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <div class="mb-4">
              <label v-if="resultCode" class="block text-sm font-medium">Result Code : {{ resultCode }}</label>
            </div>

            <div class="mb-4">
              <label v-if="debitPartyCharges" class="block text-sm font-medium">Debit Party Charges : {{ debitPartyCharges }}</label>
            </div>

            <div class="mb-4">
              <label v-if="transCompletedTime" class="block text-sm font-medium">Trans Completed Time : +{{ transCompletedTime }}</label>
            </div>

            <div class="mb-4">
              <label v-if="debitAccountBalance" class="block text-sm font-medium">Debit Account Balance : {{ debitAccountBalance }}</label>
            </div>

            <div class="mb-4">
              <label v-if="receiverPartyPublicName" class="block text-sm font-medium">Receiver Party Public Name : {{ receiverPartyPublicName }}</label>
            </div>

            <div class="mb-4">
              <label v-if="initiatorAccountCurrentBalance" class="block text-sm font-medium">Initiator Account Current Balance : {{ initiatorAccountCurrentBalance }}</label>
            </div>

            <div class="mb-4">
              <label v-if="debitPartyAffectedAccountBalance" class="block text-sm font-medium">Debit Party Affected Account Balance : {{ debitPartyAffectedAccountBalance }}</label>
            </div>

          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import { AutoTable } from '@/components/common';

export default {
  components: {
    CustomLoading,
    PageHeader,
    AutoTable
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      selectedStatus: 'All', // Initially, no filter (show all)
      filteredTaxPayment: this.taxPayments, // Initially same as taxPayments
      //
      tableHeaders: [
        { key: 'id', label: 'Id', align: 'left' },
        { key: 'prn_number', label: 'PRN Number', align: 'left' },
        { key: 'prn_amount', label: 'PRN Amount', align: 'left' },
        { key: 'prn_reg_date', label: 'PRN Reg Date', align: 'left' },
        { key: 'tax_type', label: 'Tax Type', align: 'left' },
        { key: 'response_description', label: 'Response Description', align: 'left' },
        { key: 'reciept_number', label: 'Receipt No', align: 'left' },
        { key: 'response_code', label: 'Status', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'left' }
      ],
      //
      taxPayments: [],
      taxPayment: null,
      resultCode:null,
      debitPartyCharges:null,
      transCompletedTime:null,
      debitAccountBalance:null,
      receiverPartyPublicName:null,
      initiatorAccountCurrentBalance:null,
      debitPartyAffectedAccountBalance:null,
      //
      moreParams: {
        page: '',
        timestamp: 'timestamp',
        skip_cache: '',
        start: '',
        end: '',
        limit: "10",
      },

    }
  },
  mounted() {
    this.setTaxPayments()
  },
  methods: {
    ...mapActions(["getTaxPayments", "payTax", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },
    // Filtering
    filterByStatus() {
      console.log("this.selectedStatus", this.selectedStatus)
      if (this.selectedStatus === "All") {
        this.filteredTaxPayment = this.taxPayments; // Show all if no filter
      } else {
        this.filteredTaxPayment = this.taxPayments.filter(depositData => parseInt(depositData.callback_status) === parseInt(this.selectedStatus));
      }
    },
    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setTaxPayments()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.taxPayment = data;
      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.callback_extra_data);
      console.log("extraData:", extraData)

// Extract specific properties
      this. resultCode = extraData.ResultCode;
      this. debitPartyCharges = extraData.DebitPartyCharges;
      this. transCompletedTime = extraData.TransCompletedTime;
      this. debitAccountBalance = extraData.DebitAccountBalance;
      this. receiverPartyPublicName = extraData.ReceiverPartyPublicName;
      this. initiatorAccountCurrentBalance = extraData.InitiatorAccountCurrentBalance;
      this. debitPartyAffectedAccountBalance = extraData.DebitPartyAffectedAccountBalance;
    },

    async setTaxPayments() {
      this.isLoading = true

      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      // console.log("Params: " + queryString);

      let response = await this.getTaxPayments(queryString)

      // console.log("taxPayments OK: " + JSON.stringify(response.message.result))
      if (response.status === 200) {
        this.taxPayments = response.message.result
        this.total = parseInt(response.message.record_count)

        this.showDropdown = []
        for (let i = 0; i < this.taxPayments.length; i++) {
          this.showDropdown.push(false)
        }
      }

      this.filterByStatus()
      this.isLoading = false
    },

    //
    async payTaxes(item) {
      let payload={
        "timestamp":Date.now(),
        "id":item.prn_number,
      }

      // console.log("ikjsbdncm",JSON.stringify(payload))

      this.$swal.fire({
        title: 'Are you sure?',
        text: "Confirm this action please!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          return await this.payTax(payload)
        },
      }).then(async result => {
        if (result.value.status === 200) {
          this.$swal.fire('Submitted!', result.value.message, 'success')
        } else {
          this.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },




    //
  },
}
</script>
