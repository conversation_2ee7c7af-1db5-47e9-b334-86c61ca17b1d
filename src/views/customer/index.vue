<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Customers" pageSubtitle="" />

    <!-- Search Filters Panel -->
    <div class="bg-white rounded-lg shadow-lg mx-3 mb-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium text-gray-700">Filters</h3>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
        <!-- Customer Identification Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Customer Identification</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Mobile Number</label>
              <input type="text" v-model="payload.mobile_number" placeholder="Enter mobile number"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Account Number</label>
              <input type="text" v-model="payload.acc_number" placeholder="Enter account number"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Balance Range</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Min Balance</label>
              <input type="number" v-model="payload.min_balance" placeholder="Minimum balance"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Max Balance</label>
              <input type="number" v-model="payload.max_balance" placeholder="Maximum balance"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <!-- Account Status Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Account Status</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Status</label>
              <select v-model="payload.status"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Statuses</option>
                <option value="1">Active</option>
                <option value="3">Deactivated</option>
                <option value="0">Inactive</option>
              </select>
            </div>
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Network</label>
              <select v-model="payload.network"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Networks</option>
                <option value="SAFARICOM.KE">Safaricom</option>
                <option value="AIRTEL.KE">Airtel</option>
                <option value="TELKOM.KE">Telkom</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Date Range Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
              <VueDatePicker v-model="date" range multi-calendars :enable-time-picker="false" :format="'yyyy-MM-dd'"
                :preset-ranges="presetRanges" placeholder="Select date range" class="w-full text-xs"
                @change="selectDate" @update:model-value="selectDate" />
            </div>
            <div class="flex justify-end pt-2">
              <button @click="resetFilters()"
                class="px-3 py-1 mr-2 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition-colors">
                Reset
              </button>
              <button @click="applyFilters()"
                class="px-3 py-1 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700 transition-colors">
                Apply Filters
              </button>
            </div>
          </div>
        </div>

      </div>


    </div>

    <!-- Export Buttons - Only show for users with role ID 5 and 8 -->
    <div v-if="canExport" class="bg-white rounded-lg shadow-lg mx-3 mb-4">
      <div class="p-4 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-700">Export Data</h3>
        <div class="flex space-x-2">
          <button
            @click="exportToCSV"
            :disabled="isExporting"
            class="inline-flex items-center px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-200 ease-in-out text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="!isExporting" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <svg v-else class="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isExporting ? 'Fetching Data...' : 'Export to CSV' }}
          </button>
          <button
            @click="exportToExcel"
            :disabled="isExporting"
            class="inline-flex items-center px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 transition-colors duration-200 ease-in-out text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="!isExporting" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <svg v-else class="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isExporting ? 'Fetching Data...' : 'Export to Excel' }}
          </button>
        </div>
      </div>
    </div>

    <div class="block py-4 bg-white overflow-x-auto">
      <auto-table :headers="tableHeaders" :data="profiles" :loading="isLoading" :total-items="total"
        :items-per-page="limit" :current-page-prop="offset" :server-side-pagination="true" :pagination="total > limit"
        :show-items-count="true" :has-actions="true" :get-actions="getRowActions" :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage" @items-per-page-change="handleLimitChange">
        <!-- Account Number Column -->
        <template #acc_number="{ item }">
          <div class="font-bold">{{ item.acc_number }}</div>
          <div class="text-xs text-blue-600">{{ item.ip_address }}</div>
        </template>

        <!-- Mobile Column -->
        <template #msisdn="{ item }">
          <div>
            <span class="font-bold">{{ item.msisdn }}</span>
            <div class="text-xs text-green-600">{{ item.network }}</div>
          </div>
        </template>

        <!-- Balance Column -->
        <template #balance="{ item }">
          <div>
            <div class="font-bold">KES {{ parseFloat(item.balance).toFixed(2) }}</div>
            <div class="text-xs text-green-600">Bonus: KES {{ parseFloat(item.bonus).toFixed(2) }}</div>
          </div>
        </template>

        <!-- Deposits Column -->
        <template #deposits="{ item }">
          <div>
            <div class="font-bold">KES {{ parseFloat(item.total_deposits).toFixed(2) }}</div>
            <div class="text-xs text-green-600">{{ item.deposit_count }} deposits</div>
          </div>
        </template>

        <!-- Withdrawals Column -->
        <template #withdrawals="{ item }">
          <div>
            <div class="font-bold">KES {{ parseFloat(item.total_withdrawals).toFixed(2) }}</div>
            <div class="text-xs text-green-600">{{ item.withdrawal_count }} withdrawals</div>
          </div>
        </template>

        <!-- Betting Column -->
        <template #betting="{ item }">
          <div>
            <div class="font-bold">Stake: KES {{ parseFloat(item.total_bet_stake).toFixed(2) }}</div>
            <div class="text-xs text-green-600">Wins: KES {{ parseFloat(item.total_winnings).toFixed(2) }}</div>
          </div>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <div class="whitespace-nowrap">
            <div>{{ moment(item.created_at).format('lll') }}</div>
            <div class="text-xs text-gray-500">Last login: {{ moment(item.last_login_date).fromNow() }}</div>
          </div>
        </template>

        <!-- Status Column -->
        <template #profile_status="{ item }">
          <div class="text-center">
            <span class="inline-block px-3 py-1 rounded-md text-white" :class="{
              'bg-green-500': parseInt(item.profile_status) === 1,
              'bg-red-500': parseInt(item.profile_status) === 3,
              'bg-purple-500': parseInt(item.profile_status) !== 1 && parseInt(item.profile_status) !== 3
            }">
              {{ getStatusText(item.profile_status) }}
            </span>
          </div>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item, index }">
          <action-dropdown button-text="Actions" :show-text="false" menu-class="z-50 origin-top-right">
            <action-item @click="viewCustomerDetails(item)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </template>
              View Details
            </action-item>

            <action-item @click="addProduct(item)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </template>
              Add Product
            </action-item>

            <action-item @click="viewReferrals(item)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </template>
              View Referrals
            </action-item>

            <div class="border-t border-gray-100 my-1"></div>

            <action-item v-if="parseInt(item.profile_status) === 1" @click="deActivate(item)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-red-500" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                </svg>
              </template>
              <span class="text-red-500">Deactivate Account</span>
            </action-item>

            <action-item v-if="parseInt(item.profile_status) !== 1" @click="activate(item)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-green-500" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </template>
              <span class="text-green-500">Activate Account</span>
            </action-item>
          </action-dropdown>
        </template>
      </auto-table>

      <!-- Customer Details Modal -->
      <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
        :class="{ 'opacity-100 pointer-events-auto': showDetailsModal, 'opacity-0 pointer-events-none': !showDetailsModal }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50" @click="showDetailsModal = false">
        </div>
        <div class="modal-container bg-white w-11/12 md:max-w-5xl mx-auto rounded-lg shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!-- Title -->
            <div class="flex justify-between items-center pb-3 border-b">
              <h3 class="text-xl font-medium text-gray-900">Customer Details</h3>
              <div class="modal-close cursor-pointer z-50" @click="showDetailsModal = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                  viewBox="0 0 18 18">
                  <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z" />
                </svg>
              </div>
            </div>

            <!-- Body -->
            <div class="my-4" v-if="selectedCustomer">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Account Information -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="font-semibold text-lg mb-3 text-gray-800">Account Information</h4>
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <span class="text-gray-600">Account Number:</span>
                      <span class="font-medium">{{ selectedCustomer.acc_number }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Mobile:</span>
                      <span class="font-medium">{{ selectedCustomer.msisdn }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Network:</span>
                      <span class="font-medium">{{ selectedCustomer.network }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Country:</span>
                      <span class="font-medium">{{ selectedCustomer.country }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Currency:</span>
                      <span class="font-medium">{{ selectedCustomer.currency }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Status:</span>
                      <span class="font-medium" :class="{
                        'text-green-600': parseInt(selectedCustomer.profile_status) === 1,
                        'text-red-600': parseInt(selectedCustomer.profile_status) === 3,
                        'text-purple-600': parseInt(selectedCustomer.profile_status) !== 1 && parseInt(selectedCustomer.profile_status) !== 3
                      }">
                        {{ getStatusText(selectedCustomer.profile_status) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Financial Information -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="font-semibold text-lg mb-3 text-gray-800">Financial Information</h4>
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <span class="text-gray-600">Balance:</span>
                      <span class="font-medium">{{ selectedCustomer.currency }} {{
                        parseFloat(selectedCustomer.balance).toFixed(2) }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Bonus:</span>
                      <span class="font-medium">{{ selectedCustomer.currency }} {{
                        parseFloat(selectedCustomer.bonus).toFixed(2) }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Total Deposits:</span>
                      <span class="font-medium">{{ selectedCustomer.currency }} {{
                        parseFloat(selectedCustomer.total_deposits).toFixed(2) }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Total Withdrawals:</span>
                      <span class="font-medium">{{ selectedCustomer.currency }} {{
                        parseFloat(selectedCustomer.total_withdrawals).toFixed(2) }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Total Bet Stake:</span>
                      <span class="font-medium">{{ selectedCustomer.currency }} {{
                        parseFloat(selectedCustomer.total_bet_stake).toFixed(2) }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Total Winnings:</span>
                      <span class="font-medium">{{ selectedCustomer.currency }} {{
                        parseFloat(selectedCustomer.total_winnings).toFixed(2) }}</span>
                    </div>
                  </div>
                </div>

                <!-- Activity Information -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="font-semibold text-lg mb-3 text-gray-800">Activity Information</h4>
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <span class="text-gray-600">Deposit Count:</span>
                      <span class="font-medium">{{ selectedCustomer.deposit_count }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Withdrawal Count:</span>
                      <span class="font-medium">{{ selectedCustomer.withdrawal_count }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Total Referrals:</span>
                      <span class="font-medium">{{ selectedCustomer.total_referrals }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Total Free Bets:</span>
                      <span class="font-medium">{{ selectedCustomer.total_freebets }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-gray-600">Transaction Count:</span>
                      <span class="font-medium">{{ selectedCustomer.trx_count }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Dates Information -->
              <div class="bg-gray-50 p-4 rounded-lg mt-6">
                <h4 class="font-semibold text-lg mb-3 text-gray-800">Important Dates</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p class="text-gray-600 text-sm">Account Created</p>
                    <p class="font-medium">{{ moment(selectedCustomer.created_at).format('lll') }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Last Login</p>
                    <p class="font-medium">{{ moment(selectedCustomer.last_login_date).format('lll') }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Last Activity</p>
                    <p class="font-medium">{{ moment(selectedCustomer.last_use_date).format('lll') }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">First Deposit</p>
                    <p class="font-medium">{{ selectedCustomer.first_deposit_date ?
                      moment(selectedCustomer.first_deposit_date).format('lll') : 'N/A' }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Last Deposit</p>
                    <p class="font-medium">{{ selectedCustomer.last_deposit_date ?
                      moment(selectedCustomer.last_deposit_date).format('lll') : 'N/A' }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Last Winning</p>
                    <p class="font-medium">{{ selectedCustomer.last_winning_date ?
                      moment(selectedCustomer.last_winning_date).format('lll') : 'N/A' }}</p>
                  </div>
                </div>
              </div>

              <!-- Security Information -->
              <div class="bg-gray-50 p-4 rounded-lg mt-6">
                <h4 class="font-semibold text-lg mb-3 text-gray-800">Security Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p class="text-gray-600 text-sm">Login Status</p>
                    <p class="font-medium">{{ getLoginStatusText(selectedCustomer.login_status) }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Successful Logins</p>
                    <p class="font-medium">{{ selectedCustomer.success_attempts }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Failed Logins</p>
                    <p class="font-medium">{{ selectedCustomer.failed_attempts }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Successful Resets</p>
                    <p class="font-medium">{{ selectedCustomer.successful_resets }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Failed Verifications</p>
                    <p class="font-medium">{{ selectedCustomer.failed_verify_attempts }}</p>
                  </div>
                  <div>
                    <p class="text-gray-600 text-sm">Blacklist Status</p>
                    <p class="font-medium" :class="{ 'text-red-600': selectedCustomer.blacklist_status !== '1' }">
                      {{ selectedCustomer.blacklist_status === '1' ? 'Not Blacklisted' : 'Blacklisted' }}
                    </p>
                  </div>
                </div>
                <div v-if="selectedCustomer.blacklist_status !== '1'" class="mt-3">
                  <p class="text-gray-600 text-sm">Blacklist Reason</p>
                  <p class="font-medium text-red-600">{{ selectedCustomer.blacklist_reason }}</p>
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div class="flex justify-end pt-2 border-t">
              <button @click="showDetailsModal = false"
                class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors">
                Close
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Product Modal (existing) -->
      <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
        :class="{ 'opacity-100 pointer-events-auto': openModal, 'opacity-0 pointer-events-none': !openModal }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!-- Title -->
            <div class="flex justify-between items-center pb-3">
              <p v-if="customer !== null" class="text-xl font-medium">
                Add Product<span class="font-normal"> to {{ this.customer.text }}</span></p>
              <div class="modal-close cursor-pointer z-50" @click="openModal = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                  viewBox="0 0 18 18">
                  <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z" />
                </svg>
              </div>
            </div>
            <!-- Body -->
            <div class="mb-4">
              <hr class="divider">

              <div class="grid grid-cols-2 gap-4 mb-4 mt-3">
                <div class="block">
                  <label class="text-xs font-medium mb-1 block">Select Customer {{ searchDropdownPlaceholder }}</label>
                  <input class="w-full block px-4 py-1.5 border bg-white rounded-md outline-none" v-model="searchClient"
                    @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder">
                  <ul class="absolute left-0 mt-2 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10"
                    v-if="searchDropdown">
                    <div class="max-w-xs mx-auto">
                      <li v-for="item in products" :key="item.value"
                        class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-xs font-medium"
                        @click="selectProduct(item)">{{
                        item.text }}
                      </li>
                    </div>
                  </ul>
                </div>
              </div>

              <hr class="divider">
            </div>

            <!-- Footer -->
            <div class="flex justify-end pt-2">

              <button @click="openModal = false" class="inline-block px-4 py-2 bg-neutral-100 border rounded-md mr-4">
                Cancel
              </button>
              <button @click="addToCustomer()"
                class="inline-block px-4 py-2 bg-green-500 text-white  border rounded-md ml-2 pl-10 pr-10">
                Add
              </button>
            </div>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import { mapActions } from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import $ from "jquery";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import * as XLSX from 'xlsx';

import { endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths } from "date-fns";
export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      openModal: false,
      customer: null,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      payload: {
        status: "",
        mobile_number: "",
        acc_number: "",
        network: "",
        min_balance: "",
        max_balance: "",
        start: "",
        end: "",
        page: 1,
        limit: 100,
        skip_cache: "",
        timestamp: "",
        sort: "",
        product_id: "",
        group_id: "",
      },
      profiles: [],
      moment: moment,
      showDropdown: [],
      //
      dateFormat: 'MMM DD, YYYY',
      showAdvancedFilters: false,
      //

      //
      products: [],
      moreParams: {
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
        product_status: '',
        product_name: '',
        product_id: '',
        export: '',
      },

      //cust
      form: {
        timestamp: "",
        msisdn: "",
        customer_name: "",
        email: "",
        id_type: "",
        national_id: "",
        group_id: "",
        product_id: "",
      },
      showDetailsModal: false,
      selectedCustomer: null,
      isExporting: false,
      tableHeaders: [
        { key: 'acc_number', label: 'Acc No' },
        { key: 'msisdn', label: 'Mobile' },
        { key: 'balance', label: 'Balances' },
        { key: 'deposits', label: 'Deposits' },
        { key: 'withdrawals', label: 'Withdrawals' },
        { key: 'betting', label: 'Betting' },
        { key: 'created_at', label: 'Date' },
        { key: 'profile_status', label: 'Status', align: 'center' },
        // { key: 'actions', label: 'Action', align: 'center', sortable: false }
      ],


      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'Yesterday', range: [new Date(Date.now() - 86400000), new Date(Date.now() - 86400000)]},
        {label: 'Last 7 days', range: [new Date(Date.now() - 604800000), new Date()]},
        {label: 'Last 14 days', range: [new Date(Date.now() - 1209600000), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last 3 months',
         range: [startOfMonth(subMonths(new Date(), 3)), endOfMonth(subMonths(new Date(), 1))],
        },
        {
          label:'Last 6 months',
          range: [startOfMonth(subMonths(new Date(), 6)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },
  computed: {
    // Check if current user can export (only roles 5 and 8)
    canExport() {
      // console.log("ID", JSON.stringify(this.$store.state.loggedUser));
      const userRole = this.$store.state.loggedUser.uid;
      // return userRole === '5' || userRole === '8' || userRole === 5 || userRole === 8;
      return userRole === '8' || userRole === 8;
    }
  },
  // watch network
  watch: {
    'payload.network': function (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.applyFilters()
      }
    },
  },
  //
  async mounted() {
    await this.setProfiles()
  },

  methods: {
    ...mapActions(['getProfiles', "addProfile", "toggleSideMenu",]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },

    // Get row actions for AutoTable
    getRowActions(item) {
      const actions = [
        {
          label: 'View Details',
          action: () => this.viewCustomerDetails(item),
          icon: 'fas fa-eye'
        },
        {
          label: 'Edit Customer',
          action: () => this.editCustomer(item),
          icon: 'fas fa-edit'
        }
      ];

      if (item.status === '0') {
        actions.push({
          label: 'Activate Account',
          action: () => this.activateAccount(item),
          icon: 'fas fa-check-circle'
        });
      }

      return actions;
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setProfiles();
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async setProfiles() {
      let app = this
      app.isLoading = true
      app.payload.timestamp = Date.now()

      let response = await this.getProfiles(app.payload)
      // console.log("Profiles: " + JSON.stringify(response))
      if (response.status === 200) {
        app.profiles = response.message.result
        this.total = parseInt(response.message.record_count)

        app.showDropdown = []
        for (let i = 0; i < app.profiles.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.profiles = []
        app.total = 0
      }

      app.isLoading = false
    },

    // Apply filters
    async applyFilters() {
      this.offset = 1
      this.payload.page = 1
      await this.setProfiles()
    },

    // Reset filters
    resetFilters() {
      this.payload = {
        status: "",
        mobile_number: "",
        acc_number: "",
        network: "",
        min_balance: "",
        max_balance: "",
        min_bonus: "",
        max_bonus: "",
        min_deposit: "",
        max_deposit: "",
        min_withdrawal: "",
        max_withdrawal: "",
        start: "",
        end: "",
        page: 1,
        limit: 100,
        skip_cache: "",
        timestamp: "",
        sort: "",
        product_id: "",
        group_id: "",
      }
      this.date = null
      this.offset = 1
      this.setProfiles()
    },

    //
    async addToCustomer() {
      let app = this
      app.form.timestamp = Date.now()
      // console.log("form", JSON.stringify(app.form))

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds Product to Customer!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.loading = true
          $('#addMember').html(' Please Wait ...');
          return await this.addProfile(payload)

        },
      })
        .then(async (result) => {
          $('#addMember').html('Add');
          app.loading = false
          if (result.value.status === 200) {
            app.$swal.fire({
              title: 'Added!',
              text: result.value.message,
              icon: 'success'
            }).then(async (result) => {
              await app.$router.push({ name: 'roles' })
            })

          } else {
            app.$swal.fire('Error!', result.value.message, 'error')
          }
        })
    },

    //
    gotToPage(page) {
      let vm = this
      vm.payload.page = page
      vm.offset = page
      vm.setProfiles()
    },

    //
    async selectDate() {
      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.payload.start = '';
        this.payload.end = '';
        await this.setProfiles();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      this.payload.start = this.formatDate(this.date[0]);
      this.payload.end = this.formatDate(this.date[1]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      await this.setProfiles();
    },

    //
    formatDate(date) {
      let d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    //
    async viewReferrals(row) {
      this.closeDropdown()
      await this.$router.push({ name: 'referrals', query: { msisdn: row.msisdn } })
    },

    //
    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },
    viewCustomerDetails(customer) {
      this.selectedCustomer = customer;
      this.showDetailsModal = true;
      this.closeDropdown();
    },
    getStatusText(status) {
      const statusMap = {
        '1': 'Active',
        '3': 'Deactivated',
        '0': 'Inactive'
      };
      return statusMap[status] || 'Unknown';
    },
    getLoginStatusText(status) {
      const statusMap = {
        '1': 'Active',
        '2': 'Pending Verification',
        '3': 'Locked',
        '4': 'Suspended',
        '5': 'Deactivated',
        '6': 'New'
      };
      return statusMap[status] || 'Unknown';
    },

    // Fetch export data from API with pagination
    async fetchExportData() {
      let app = this;
      app.isExporting = true;

      let allData = [];
      let currentPage = 1;
      let totalRecords = 0;
      let hasMoreData = true;

      try {
        // Show initial progress
        app.$swal.fire({
          title: 'Preparing Export',
          text: 'Fetching data...',
          allowOutsideClick: false,
          didOpen: () => {
            app.$swal.showLoading();
          }
        });

        while (hasMoreData) {
          // Create export payload with current filters, pagination, and no export flag
          const exportPayload = {
            ...app.payload,
            page: currentPage,
            limit: 1000,
            timestamp: Date.now()
          };

          // Remove export flag to get normal data
          delete exportPayload.export;

          let response = await this.getProfiles(exportPayload);

          if (response.status === 200 && response.message.result) {
            const pageData = response.message.result;
            allData = allData.concat(pageData);

            // Get total records from first response
            if (currentPage === 1) {
              totalRecords = parseInt(response.message.record_count) || 0;
            }

            // Update progress
            const currentCount = allData.length;
            const progressText = `Fetched ${currentCount} of ${totalRecords} records...`;
            app.$swal.update({
              text: progressText
            });

            // Check if we have more data
            if (pageData.length < 1000 || currentCount >= totalRecords) {
              hasMoreData = false;
            } else {
              currentPage++;
            }
          } else {
            hasMoreData = false;
            if (currentPage === 1) {
              // No data at all
              app.$swal.close();
              app.$swal.fire({
                icon: 'warning',
                title: 'No Data',
                text: 'No data available to export.'
              });
              return null;
            }
          }
        }

        app.$swal.close();

        if (allData.length === 0) {
          app.$swal.fire({
            icon: 'warning',
            title: 'No Data',
            text: 'No data available to export.'
          });
          return null;
        }

        // Show success message with final count
        app.$swal.fire({
          icon: 'success',
          title: 'Data Fetched',
          text: `Successfully fetched ${allData.length} records for export.`,
          timer: 2000,
          showConfirmButton: false
        });

        return allData;

      } catch (error) {
        console.error('Export error:', error);
        app.$swal.close();
        app.$swal.fire({
          icon: 'error',
          title: 'Export Failed',
          text: 'An error occurred while fetching export data.'
        });
        return null;
      } finally {
        app.isExporting = false;
      }
    },

    // Export to CSV
    async exportToCSV() {
      // Fetch export data from API
      const exportData = await this.fetchExportData();
      if (!exportData || exportData.length === 0) {
        return; // fetchExportData already shows error messages
      }

      // Show progress for file generation
      this.$swal.fire({
        title: 'Generating CSV',
        text: `Processing ${exportData.length} records...`,
        allowOutsideClick: false,
        didOpen: () => {
          this.$swal.showLoading();
        }
      });

      try {
        // Prepare CSV data with the specified keys
        const csvData = [];

        // Add header row
        csvData.push([
          'Name',
          'Mobile Number',
          'Balance',
          'Total Deposits',
          'Total Withdrawals',
          'Total Bet Stake',
          'Total Winnings',
          'Last Login Date',
          'Created At'
        ]);

        // Add data rows
        exportData.forEach(profile => {
          csvData.push([
            profile.name || profile.acc_number || '',
            profile.msisdn || '',
            profile.balance || '0.00',
            profile.total_deposits || '0.00',
            profile.total_withdrawals || '0.00',
            profile.total_bet_stake || '0.00',
            profile.total_winnings || '0.00',
            profile.last_login_date || '',
            profile.created_at || ''
          ]);
        });

        // Convert to CSV string
        const csvContent = csvData.map(row =>
          row.map(field => `"${field}"`).join(',')
        ).join('\n');

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `customers_export_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$swal.close();
        this.$swal.fire({
          icon: 'success',
          title: 'Export Complete',
          text: `CSV file with ${exportData.length} records has been downloaded.`,
          timer: 3000,
          showConfirmButton: false
        });

      } catch (error) {
        console.error('CSV generation error:', error);
        this.$swal.close();
        this.$swal.fire({
          icon: 'error',
          title: 'Export Failed',
          text: 'An error occurred while generating the CSV file.'
        });
      }
    },

    // Export to Excel
    async exportToExcel() {
      // Fetch export data from API
      const exportData = await this.fetchExportData();
      if (!exportData || exportData.length === 0) {
        return; // fetchExportData already shows error messages
      }

      // Show progress for file generation
      this.$swal.fire({
        title: 'Generating Excel',
        text: `Processing ${exportData.length} records...`,
        allowOutsideClick: false,
        didOpen: () => {
          this.$swal.showLoading();
        }
      });

      try {
        // Prepare Excel data with the specified keys
        const excelData = [];

        // Add header row
        excelData.push([
          'Name',
          'Mobile Number',
          'Balance',
          'Total Deposits',
          'Total Withdrawals',
          'Total Bet Stake',
          'Total Winnings',
          'Last Login Date',
          'Created At'
        ]);

        // Add data rows
        exportData.forEach(profile => {
          excelData.push([
            profile.name || profile.acc_number || '',
            profile.msisdn || '',
            parseFloat(profile.balance || 0),
            parseFloat(profile.total_deposits || 0),
            parseFloat(profile.total_withdrawals || 0),
            parseFloat(profile.total_bet_stake || 0),
            parseFloat(profile.total_winnings || 0),
            profile.last_login_date || '',
            profile.created_at || ''
          ]);
        });

        // Create workbook and worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.aoa_to_sheet(excelData);

        // Set column widths for better formatting
        const columnWidths = [
          { wch: 25 }, // Name
          { wch: 15 }, // Mobile Number
          { wch: 12 }, // Balance
          { wch: 15 }, // Total Deposits
          { wch: 15 }, // Total Withdrawals
          { wch: 15 }, // Total Bet Stake
          { wch: 15 }, // Total Winnings
          { wch: 20 }, // Last Login Date
          { wch: 20 }  // Created At
        ];
        worksheet['!cols'] = columnWidths;

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers');

        // Generate and download Excel file
        const fileName = `customers_export_${moment().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
        XLSX.writeFile(workbook, fileName);

        this.$swal.close();
        this.$swal.fire({
          icon: 'success',
          title: 'Export Complete',
          text: `Excel file with ${exportData.length} records has been downloaded.`,
          timer: 3000,
          showConfirmButton: false
        });

      } catch (error) {
        console.error('Excel generation error:', error);
        this.$swal.close();
        this.$swal.fire({
          icon: 'error',
          title: 'Export Failed',
          text: 'An error occurred while generating the Excel file.'
        });
      }
    }
  }
}
</script>

<style scoped>
/* Add any additional styles here */
.modal {
  transition: opacity 0.25s ease;
}

/* Ensure dropdowns are on top */
:deep(.action-dropdown) {
  z-index: 50 !important;
}

:deep(.action-dropdown .absolute) {
  z-index: 50 !important;
}


/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}
</style>
