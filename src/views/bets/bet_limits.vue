<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Bet" pageSubtitle="Limits" />

    <!-- Bet Limits Table using AutoTable component -->
    <auto-table
      :headers="tableHeaders"
      :data="dataList"
      :has-actions="true"
      :get-actions="getRowActions"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :items-per-page-options="[10, 25, 50, 100]"
      @page-change="handlePageChange"
      @items-per-page-change="handleLimitChange"
    >
      <!-- Index Column -->
      <template #index="{ index }">
        <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
      </template>

      <!-- Provider Column -->
      <template #bet_name="{ item }">
        <div class="text-center provider-badge"
             :class="getProviderClass(item.bet_name)" 
             :title="item.bet_name">
          {{ item.bet_name }}
        </div>
      </template>

      <!-- Max Stake Column -->
      <template #max_stake="{ item }">
        <div class="text-center">{{ item.currency }}. {{ item.max_stake }}</div>
      </template>

      <!-- Min Stake Column -->
      <template #min_stake="{ item }">
        <div class="text-center">{{ item.currency }}. {{ item.min_stake }}</div>
      </template>

      <!-- Max Win Column -->
      <template #max_win="{ item }">
        <div class="text-center">{{ item.currency }}. {{ item.max_win }}</div>
      </template>

      <!-- Risk Approval Column -->
      <template #risk_approval_amount="{ item }">
        <div class="text-center">{{ item.currency }}. {{ item.risk_approval_amount }}</div>
      </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <div class="status-badge" :class="getStatusClass(item.status)">
          {{ getStatusText(item.status) }}
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <action-dropdown button-text="Actions" :show-text="false">
          <!-- Status Toggle Option -->
          <action-item
            v-if="item.status === '1'"
            text="Disable"
            color="red"
            @click="activateDeActivate(item, '0')"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
            </template>
          </action-item>

          <action-item
            v-else
            text="Enable"
            color="green"
            @click="activateDeActivate(item, '1')"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item>

          <!-- Edit Option -->
          <action-item
            text="Edit Limits"
            color="blue"
            @click="editBetLimit(item)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </template>
          </action-item>
        </action-dropdown>
      </template>
    </auto-table>

  </div>
</template>

<script>
import {mapActions} from "vuex";
import moment from "moment-timezone";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      //
      moreParams: {
        status: "",
        start: "",
        end: "",
        page: 1,
        limit: 10,
        timestamp: Date.now(),
        skip_cache: false,
        sort:"DESC"
      },
      dataList: [],
      tableHeaders: [
        { key: "index", label: "#", align: "center" },
        { key: "bet_name", label: "Provider", align: "center" },
        { key: "max_stake", label: "Max Stake", align: "center" },
        { key: "min_stake", label: "Min Stake", align: "center" },
        { key: "max_win", label: "Max Win", align: "center" },
        { key: "risk_approval_amount", label: "Risk Approval", align: "center" },
        { key: "status", label: "Status", align: "center" }
      ],
      rowHighlightConditions: {
        '0': 'bg-red-50',  // Inactive rows with light red background
        '1': '',          // Active rows with default background
        '2': 'bg-yellow-50' // Pending rows with light yellow background
      },
      editingItem: null,


      statuses: [
        {text: 'Active', value: 1},
        {text: 'Pending Approval', value: 2},
        {text: 'Suspended', value: 3},
      ],
    }

  },
  async mounted() {
    await this.setBetLimits()
  },

  methods: {
    ...mapActions(["getBetLimits","updateBetLimit", "fillPayBill", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    // Get row actions for AutoTable
    getRowActions(item) {
      const actions = [];
      if (item.status === '1') {
        actions.push({
          label: 'Disable',
          action: () => this.activateDeActivate(item, '0'),
          icon: 'fas fa-times-circle'
        });
      } else {
        actions.push({
          label: 'Enable',
          action: () => this.activateDeActivate(item, '1'),
          icon: 'fas fa-check-circle'
        });
      }
      actions.push({
        label: 'Edit Limits',
        action: () => this.editBetLimit(item),
        icon: 'fas fa-edit'
      });
      return actions;
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setBetLimits();
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    getProviderClass(providerName) {
      // Map provider names to CSS classes
      const classMap = {
        'GENIUS': 'provider-genius',
        'BETRADAR': 'provider-betradar',
        'BETGAMES': 'provider-betgames',
        'EVOLUTION': 'provider-evolution',
        'PRAGMATIC': 'provider-pragmatic',
        'SPORTY': 'provider-sporty',
        'KIRON': 'provider-kiron',
        'EZUGI': 'provider-ezugi',
        'GOLDENRACE': 'provider-goldenrace',
        'PLAYTECH': 'provider-playtech',
        'MICROGAMING': 'provider-microgaming',
        'NETENT': 'provider-netent',
        'QUICKSPIN': 'provider-quickspin',
        'SPINOMENAL': 'provider-spinomenal',
      };

      // Return the class name or default
      return classMap[providerName] || 'provider-default';
    },

    getStatusClass(status) {
      const statusMap = {
        '0': 'status-inactive',
        '1': 'status-active',
        '2': 'status-pending'
      };

      return statusMap[status] || 'status-default';
    },

    getStatusText(status) {
      const statusMap = {
        '0': 'Inactive',
        '1': 'Active',
        '2': 'Pending'
      };

      return statusMap[status] || 'Unknown';
    },

    //
    async setBetLimits() {
      let app = this
      app.isLoading = true

      let response = await this.getBetLimits(app.moreParams)
      if (response.status === 200) {
        app.dataList = response.message.result || []

        // Set total count for pagination
        if (response.message.record_count) {
          app.total = parseInt(response.message.record_count)
        } else if (response.message.total_count) {
          app.total = parseInt(response.message.total_count)
        } else {
          app.total = app.dataList.length
        }

        // Initialize dropdown state array
        app.showDropdown = Array(app.dataList.length).fill(false)
      } else {
        app.dataList = []
        app.total = 0
        app.showDropdown = []
      }

      app.isLoading = false
    },

    handlePageChange(page) {
      this.offset = page
      this.moreParams.page = page
      this.moreParams.limit = this.limit
      this.setBetLimits()
    },


    async activateDeActivate(item, status) {
      let app = this;

      const payload = {
        "id": item.id.toString(),
        "timestamp": Date.now(),
        "status": status.toString(),
      }

      const statusText = status === '1' ? 'activate' : 'deactivate';
      const statusAction = status === '1' ? 'Activation' : 'Deactivation';

      app.$swal.fire({
        title: `${statusAction} Confirmation`,
        text: `Are you sure you want to ${statusText} this bet limit for ${item.bet_name}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true;
          return await this.updateBetLimit(payload);
        },
      })
      .then(async (result) => {
        app.isLoading = false;

        if (result.value && result.value.status === 200) {
          app.$swal.fire({
            title: 'Updated!',
            text: result.value.message,
            icon: 'success'
          });
          // Refresh the data after successful update
          await app.setBetLimits();
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error');
        }

        // Close the dropdown
        app.closeDropdown();
      });
    },

    editBetLimit(item) {
      this.editingItem = { ...item };

      // Close the dropdown
      this.closeDropdown();

      this.$swal.fire({
        title: 'Edit Bet Limits',
        html: `
          <div class="text-left mb-4">
            <p class="font-bold text-lg mb-2">${item.bet_name} Limits</p>
            <div class="mb-3">
              <label class="block text-sm font-medium text-gray-700 mb-1">Min Stake</label>
              <input id="min-stake" type="number" class="w-full p-2 border rounded" value="${item.min_stake}">
            </div>
            <div class="mb-3">
              <label class="block text-sm font-medium text-gray-700 mb-1">Max Stake</label>
              <input id="max-stake" type="number" class="w-full p-2 border rounded" value="${item.max_stake}">
            </div>
            <div class="mb-3">
              <label class="block text-sm font-medium text-gray-700 mb-1">Max Win</label>
              <input id="max-win" type="number" class="w-full p-2 border rounded" value="${item.max_win}">
            </div>
            <div class="mb-3">
              <label class="block text-sm font-medium text-gray-700 mb-1">Risk Approval Amount</label>
              <input id="risk-approval" type="number" class="w-full p-2 border rounded" value="${item.risk_approval_amount}">
            </div>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Save Changes',
        confirmButtonColor: '#3085d6',
        cancelButtonText: 'Cancel',
        cancelButtonColor: '#d33',
        showLoaderOnConfirm: true,
        preConfirm: () => {
          // Get values from the form
          const minStake = document.getElementById('min-stake').value;
          const maxStake = document.getElementById('max-stake').value;
          const maxWin = document.getElementById('max-win').value;
          const riskApproval = document.getElementById('risk-approval').value;

          // Validate inputs
          if (!minStake || !maxStake || !maxWin || !riskApproval) {
            this.$swal.showValidationMessage('All fields are required');
            return false;
          }

          if (parseFloat(minStake) <= 0 || parseFloat(maxStake) <= 0 || parseFloat(maxWin) <= 0) {
            this.$swal.showValidationMessage('Values must be greater than zero');
            return false;
          }

          if (parseFloat(minStake) > parseFloat(maxStake)) {
            this.$swal.showValidationMessage('Min Stake cannot be greater than Max Stake');
            return false;
          }

          // Return the values for the next step
          return {
            min_stake: minStake,
            max_stake: maxStake,
            max_win: maxWin,
            risk_approval_amount: riskApproval
          };
        }
      }).then(async (result) => {
        if (result.isConfirmed) {
          this.isLoading = true;

          const payload = {
            id: item.id.toString(),
            timestamp: Date.now(),
            min_stake: result.value.min_stake,
            max_stake: result.value.max_stake,
            max_win: result.value.max_win,
            risk_approval_amount: result.value.risk_approval_amount
          };

          try {
            const response = await this.updateBetLimit(payload);

            if (response.status === 200) {
              this.$swal.fire('Updated!', response.message, 'success');
              await this.setBetLimits();
            } else {
              this.$swal.fire('Error!', response.message || 'Failed to update bet limits', 'error');
            }
          } catch (error) {
            this.$swal.fire('Error!', 'An unexpected error occurred', 'error');
            console.error('Error updating bet limits:', error);
          } finally {
            this.isLoading = false;
            this.editingItem = null;
          }
        }
      });
    },

    //
    toggleDetails(item) {
      console.log("paybills item: ", item)
      // this.$router.push({name: 'paybills-view', params: {id: item.id}})
    },

    // Note: These methods are no longer needed as the ActionDropdown component handles this internally
    // They are kept for backward compatibility with other components that might still use them
    toggleDropdown(index) {
      // Make a copy of the array to ensure reactivity
      const newDropdownState = [...this.showDropdown];
      for (let i = 0; i < newDropdownState.length; i++) {
        newDropdownState[i] = i === index ? !newDropdownState[i] : false;
      }
      this.showDropdown = newDropdownState;
    },

    closeDropdown() {
      // Reset all dropdowns to closed state
      this.showDropdown = this.showDropdown.map(() => false);
    },


  },
}
</script>

<style scoped>
.provider-badge {
  padding: 8px 12px;
  border-radius: 20px;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-block;
  min-width: 200px;
  text-align: center;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 0.7rem;
}

.provider-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  filter: brightness(110%);
}

/* Provider-specific styles */
.provider-genius {
  background-color: #008000;
}

.provider-betradar {
  background-color: #FF5733;
}

.provider-betgames {
  background-color: #9B59B6;
}

.provider-evolution {
  background-color: #3498DB;
}

.provider-pragmatic {
  background-color: #F1C40F;
  color: #333; /* Dark text for better contrast on yellow */
}

.provider-sporty {
  background-color: #E74C3C;
}

.provider-kiron {
  background-color: #1ABC9C;
}

.provider-ezugi {
  background-color: #D35400;
}

.provider-goldenrace {
  background-color: #8E44AD;
}

.provider-playtech {
  background-color: #16A085;
}

.provider-microgaming {
  background-color: #2980B9;
}

.provider-netent {
  background-color: #C0392B;
}

.provider-quickspin {
  background-color: #27AE60;
}

.provider-spinomenal {
  background-color: #7D3C98;
}

.provider-default {
  background-color: #2c6dff;
}

/* Bet type badges */
.bet-type-badge {
  padding: 6px 10px;
  border-radius: 16px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-block;
  text-align: center;
  min-width: 100px;
  letter-spacing: 0.3px;
  font-size: 0.75rem;
}

.bet-type-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.bet-type-cash {
  background-color: #2ecc71; /* Green */
}

.bet-type-bonus {
  background-color: #f39c12; /* Orange */
}

.bet-type-free {
  background-color: #3498db; /* Blue */
}

.bet-type-default {
  background-color: #95a5a6; /* Gray */
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.status-active {
  background-color: #2ecc71; /* Green */
}

.status-inactive {
  background-color: #e74c3c; /* Red */
}

.status-pending {
  background-color: #f39c12; /* Orange */
}

.status-default {
  background-color: #95a5a6; /* Gray */
}
</style>
