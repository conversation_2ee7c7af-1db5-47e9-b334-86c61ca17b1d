<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <!-- Main View with Carousel -->
    <div>
      <page-header pageName="Bet" pageSubtitle="Blocked IPs" />
      
      <!-- Main IPs Table (Slide 0) -->
      <div class="block py-4 bg-white overflow-x-auto transition-all duration-300 ease-in-out"
           :class="{'translate-x-0 opacity-100': !isAccountsViewOpen, '-translate-x-full opacity-0 absolute inset-0': isAccountsViewOpen}">
        <!-- Bet IPs Table using DataTable component -->
        <auto-table
          :headers="tableHeaders"
          :data="dataList"
          :has-actions="true"
          :get-actions="getRowActions"
          :total-items="total"
          :items-per-page="limit"
          :current-page-prop="offset"
          :server-side-pagination="true"
          :pagination="total > limit"
          :show-items-count="true"
          :items-per-page-options="[10, 25, 50, 100]"
          @page-change="handlePageChange"
          @items-per-page-change="handleLimitChange"
        >
          <!-- Index Column -->
          <template #index="{ index }">
            <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
          </template>

          <!-- IP Address Column -->
          <template #ip_address="{ item }">
            <div class="text-center font-bold">
              {{ item.ip_address }}
            </div>
          </template>

          <!-- Provider Column -->
          <template #blocked_upto_date="{ item }">
            <div class="text-center">
              {{ item.blocked_upto_date }}
            </div>
          </template>

          <!-- Status Column -->
          <template #status="{ item }">
            <div class="status-badge w-full text-center" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </div>
          </template>

          <!-- Actions Column -->
          <template #actions="{ item, index }">
            <div class="relative" style="position: static;">
              <action-dropdown 
                button-text="Actions" 
                :show-text="false"
                menu-class="action-dropdown"
              >
                <!-- Block IP Option -->
                <action-item v-if="item.status == '0'"
                  text="Block IP"
                  color="red"
                  @click="activateDeActivate(item, 1)"
                >
                  <template #icon>
                    <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </template>
                </action-item>

                <!-- Unblock IP Option -->
                <action-item v-else
                  text="Unblock IP"
                  color="green"
                  @click="activateDeActivate(item, 0)"
                >
                  <template #icon>
                    <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 018 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                    </svg>
                  </template>
                </action-item>

                <!-- View Accounts Option -->
                <action-item
                  text="View Accounts"
                  color="blue"
                  @click="viewAccounts(item)"
                >
                  <template #icon>
                    <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </template>
                </action-item>
              </action-dropdown>
            </div>
          </template>
        </auto-table>
      </div>
      
      <!-- Accounts View (Slide 1) -->
      <div class="block py-4 bg-white overflow-x-auto transition-all duration-300 ease-in-out"
           :class="{'translate-x-0 opacity-100': isAccountsViewOpen, 'translate-x-full opacity-0 absolute inset-0': !isAccountsViewOpen}">
        <div class="p-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold flex items-center">
              <button @click="closeAccountsView" class="mr-3 p-2 rounded-full hover:bg-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <span>Accounts Using IP</span>
              <span v-if="currentIpAddress" class="text-sm font-normal text-gray-500 ml-2">
                ({{ currentIpAddress }})
              </span>
            </h3>
          </div>
          
          <!-- Accounts Table -->
          <auto-table
            v-if="accountsData.length > 0"
            :headers="accountsTableHeaders"
            :data="accountsData"
            :has-actions="true"
            :get-actions="getAccountsRowActions"
            :total-items="accountsTotal"
            :items-per-page="accountsLimit"
            :current-page-prop="accountsOffset"
            :server-side-pagination="true"
            :pagination="accountsTotal > accountsLimit"
            :show-items-count="true"
            :items-per-page-options="[10, 25, 50, 100]"
            @page-change="handleAccountsPageChange"
            @items-per-page-change="handleAccountsLimitChange"
          >
            <!-- Bet ID Column -->
            <template #bet_id="{ item }">
              <div>{{ item.bet_id }}</div>
            </template>

            <!-- Bet Reference Column -->
            <template #bet_reference="{ item }">
              <div>
                <span class="font-bold">{{ item.bet_reference }}</span>
                <br>
                <span style="font-size: 11px;">{{ item.bet_attribution || '' }}</span>
              </div>
            </template>

            <!-- Mobile Column -->
            <template #mobile="{ item }">
              <div>{{ item.mobile }}</div>
            </template>

            <!-- Stake Column -->
            <template #bet_amount="{ item }">
              <div>{{ formatNumber(item.bet_amount) }}</div>
            </template>

            <!-- Possible Win Column -->
            <template #possible_win="{ item }">
              <div>
                <strong>{{ formatNumber(item.possible_win) }}</strong>
                <br>
                <span style="font-size: 11px;">W.Tax: {{ formatNumber(item.witholding_tax) }}</span>
              </div>
            </template>

            <!-- Bet Type Column -->
            <template #bet_type="{ item }">
              <div class="bet-type-badge" :class="getBetTypeClass(item.bet_type)">
                {{ getBetTypeText(item.bet_type) }}
              </div>
            </template>

            <!-- Status Column -->
            <template #status="{ item }">
              <div class="status-badge" :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </div>
            </template>

            <!-- Date Column -->
            <template #created_at="{ item }">
              <div>{{ moment(item.created_at).format('YYYY-MM-DD HH:mm') }}</div>
            </template>

            <!-- Actions Column -->
            <template #actions="{ item }">
              <action-dropdown button-text="Actions" :show-text="false">
                <action-item
                  text="View Bet Slip"
                  color="blue"
                  @click="viewBetSlip(item)"
                >
                  <template #icon>
                    <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </template>
                </action-item>
              </action-dropdown>
            </template>
          </auto-table>

          <!-- No Data Message -->
          <div v-else class="py-8 text-center text-gray-500">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No Data Found</h3>
            <p class="mt-1 text-sm text-gray-500">No accounts found using this IP address.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapActions} from "vuex";
import moment from "moment-timezone";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      //
      moreParams: {
        status: "",
        start: "",
        end: "",
        page: 1,
        limit: 10,
        timestamp: Date.now(),
        skip_cache: false,
        sort:"DESC"
      },
      dataList: [],
      tableHeaders: [
        { key: "index", label: "#", align: "center" },
        { key: "ip_address", label: "IP Address", align: "center" },
        { key: "blocked_upto_date", label: "Blocked Upto Date", align: "center" },
        { key: "status", label: "Status", align: "center" },
        { key: "updated_at", label: "Updated At", align: "center" }
      ],
      rowHighlightConditions: {
        '0': 'bg-red-50',  // Inactive rows with light red background
        '1': '',          // Active rows with default background
      },
      
      // Accounts view properties
      isAccountsViewOpen: false,
      currentIpAddress: null,
      accountsData: [],
      accountsTotal: 0,
      accountsOffset: 1,
      accountsLimit: 10,
      accountsTableHeaders: [
        { key: "bet_id", label: "Bet ID", align: "left" },
        { key: "bet_reference", label: "Reference", align: "left" },
        { key: "mobile", label: "Mobile", align: "left" },
        { key: "bet_amount", label: "Stake", align: "right" },
        { key: "possible_win", label: "Possible Win", align: "right" },
        { key: "bet_type", label: "Bet Type", align: "center" },
        { key: "status", label: "Status", align: "center" },
        { key: "created_at", label: "Date", align: "center" },
        { key: "actions", label: "Actions", align: "center" }
      ]
    }
  },
  async mounted() {
    await this.setBlockedIPs()
  },

  methods: {
    ...mapActions([
      "getBlockedIPs",
      "updateBlockedIP", 
      "getBlockedIPProfiles", // Add this action for fetching customers
      "toggleSideMenu",
    ]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    getRowActions(item) {
      return [
        {
          label: 'View Accounts',
          action: () => this.viewAccounts(item.ip_address),
          icon: 'eye',
          class: 'text-blue-600'
        },
        {
          label: 'Update Status',
          action: () => this.updateStatus(item),
          icon: 'edit',
          class: 'text-green-600'
        }
      ];
    },

    getAccountsRowActions(item) {
      return [
        {
          label: 'View Bet Details',
          action: () => this.viewBetDetails(item),
          icon: 'eye',
          class: 'text-blue-600'
        }
      ];
    },

    async setBlockedIPs() {
      let app = this
      app.isLoading = true
      let response = await this.getBlockedIPs(app.moreParams)
      
      if (response.status === 200) {
        app.dataList = response.message.result

        // Set total count for pagination
        if (response.message.record_count) {
          app.total = parseInt(response.message.record_count)
        } else if (response.message.total_count) {
          app.total = parseInt(response.message.total_count)
        } else {
          app.total = app.dataList.length
        }
        // Initialize dropdown state array
        app.showDropdown = Array(app.dataList.length).fill(false)
      }

      app.isLoading = false
    },

    async activateDeActivate(item, status) {
      let app = this;

      const payload = {
        "id": item.id.toString(),
        "timestamp": Date.now(),
        "status": status.toString(),
      }

      const statusText = status === 1 ? 'activate' : 'deactivate';
      const statusAction = status === 1 ? 'Activation' : 'Deactivation';
      const newStatusText = status === 1 ? 'Active' : 'Inactive';

      app.$swal.fire({
        title: `${statusAction} Confirmation`,
        text: `Are you sure you want to ${statusText} this IP address ${item.ip_address}? Status will be changed to "${newStatusText}".`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true;
          return await this.updateBlockedIP(payload);
        },
      })
      .then(async (result) => {
        app.isLoading = false;

        if (result.value && result.value.status === 200) {
          app.$swal.fire({
            title: 'Updated!',
            text: result.value.message,
            icon: 'success'
          });
          // Refresh the data after successful update
          await app.setBlockedIPs();
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error');
        }

        // Close all dropdowns
        app.closeDropdown();
      });
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    handlePageChange(page) {
      this.offset = page;
      this.moreParams.page = page;
      this.moreParams.limit = this.limit;
      this.setBlockedIPs();
    },

    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.offset = 1;
      this.moreParams.page = 1;
      this.moreParams.limit = newLimit;
      this.setBlockedIPs();
    },

    getStatusClass(status) {
      const statusMap = {
        '0': 'status-inactive',
        '1': 'status-active',
        '3': 'status-suspended',
      };

      return statusMap[status] || 'status-default';
    },

    getStatusText(status) {
      const statusMap = {
        '0': 'Inactive',
        '1': 'Active',
        '3': 'Suspended',
      };

      return statusMap[status] || 'Unknown';
    },

    // Combined method for blocking/unblocking IPs
    toggleIPBlock(item, status) {
      let app = this;
      
      if (status === 1) {
        // Block IP
        app.$swal.fire({
          title: 'Block IP Address',
          text: `Enter the number of days to block IP address ${item.ip_address}:`,
          input: 'number',
          inputAttributes: {
            min: 1,
            step: 1
          },
          inputValue: 7, // Default 7 days
          showCancelButton: true,
          confirmButtonText: 'Block IP',
          showLoaderOnConfirm: true,
          preConfirm: async (days) => {
            if (!days || days < 1) {
              app.$swal.showValidationMessage('Please enter a valid number of days');
              return false;
            }
            
            app.isLoading = true;
            
            // Calculate the block until date
            const blockUntil = new Date();
            blockUntil.setDate(blockUntil.getDate() + parseInt(days));
            
            const payload = {
              "ip_address": item.ip_address,
              "blocked_upto_date": blockUntil.toISOString().split('T')[0], // Format as YYYY-MM-DD
              "status": status.toString(),
              "timestamp": Date.now()
            };
            
            return await this.updateBlockedIP(payload);
          },
        }).then(handleResponse);
      } else {
        // Unblock IP
        app.$swal.fire({
          title: 'Unblock IP Address',
          text: `Are you sure you want to unblock IP address ${item.ip_address}?`,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Yes, unblock it!',
          showLoaderOnConfirm: true,
          preConfirm: async () => {
            app.isLoading = true;
            
            const payload = {
              "ip_address": item.ip_address,
              "blocked_upto_date": null, // Clear the block date
              "status": status.toString(),
              "timestamp": Date.now()
            };
            
            return await this.updateBlockedIP(payload);
          },
        }).then(handleResponse);
      }
      
      // Common response handler
      async function handleResponse(result) {
        app.isLoading = false;
        
        if (result.value && result.value.status === 200) {
          const actionText = status === 1 ? 'Blocked' : 'Unblocked';
          app.$swal.fire({
            title: `IP ${actionText}!`,
            text: `IP address ${item.ip_address} has been ${actionText.toLowerCase()} successfully.`,
            icon: 'success'
          });
          // Refresh the data
          await app.setBlockedIPs();
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error');
        }
      }
    },

    // Update viewAccounts method to use slide-based transition
    async viewAccounts(item) {
      let app = this;
      app.isLoading = true;
      app.currentIpAddress = item.ip_address;
      app.closeDropdown(); // Close dropdown menu
      
      // Create params object
      const params = {
        ip: item.ip_address,
        timestamp: Date.now(),
        page: 1,
        limit: app.accountsLimit,
        skip_cache: true
      };
      
      // call the API
      let response = await this.getBlockedIPProfiles(params);

      if (response.status === 200) {
        app.accountsData = response.message.result || [];
        app.accountsTotal = parseInt(response.message.record_count || response.message.total_count || 0);
        app.accountsOffset = 1;
      } else {
        // Show "no data" message
        app.accountsData = [];
        app.accountsTotal = 0;
      }

      // Open accounts view with slide transition
      app.isAccountsViewOpen = true;
      app.isLoading = false;
    },

    // Close accounts view
    closeAccountsView() {
      this.isAccountsViewOpen = false;
      
      // Clear data when closing
      setTimeout(() => {
        this.accountsData = [];
        this.accountsTotal = 0;
      }, 300); // Clear after transition completes
    },

    // Handle accounts table pagination
    handleAccountsPageChange(page) {
      this.accountsOffset = page;
      
      // Create params object for fetching the new page
      const params = {
        ip: this.currentIpAddress,
        timestamp: Date.now(),
        page: page,
        limit: this.accountsLimit,
        skip_cache: true
      };
      
      // Fetch the new page of accounts
      this.fetchAccountsPage(params);
    },

    handleAccountsLimitChange(newLimit) {
      this.accountsLimit = newLimit;
      this.accountsOffset = 1;

      const params = {
        ip: this.currentIpAddress,
        timestamp: Date.now(),
        page: 1,
        limit: newLimit,
        skip_cache: true
      };

      this.fetchAccountsPage(params);
    },
    
    // Fetch a specific page of accounts
    async fetchAccountsPage(params) {
      let app = this;
      app.isLoading = true;
      
      let response = await this.getBlockedIPProfiles(params);
      
      if (response.status === 200) {
        app.accountsData = response.message.result || [];
        app.accountsTotal = parseInt(response.message.record_count || response.message.total_count || 0);
      } else {
        app.accountsData = [];
        app.accountsTotal = 0;
      }
      
      app.isLoading = false;
    },

    // Format numbers with commas
    formatNumber(value) {
      if (!value) return '0.00';
      return parseFloat(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    },
    
    // Get bet type class
    getBetTypeClass(type) {
      const typeMap = {
        'cash': 'bg-blue-500 text-white',
        'bonus': 'bg-purple-500 text-white',
        'free': 'bg-green-500 text-white'
      };
      
      return typeMap[type?.toLowerCase()] || 'bg-gray-500 text-white';
    },
    
    // Get bet type text
    getBetTypeText(type) {
      if (!type) return 'Unknown';
      return type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
    },

    // Since we removed the bet slip modal, we need to update these methods
    viewAllBetSlips() {
      // Implement alternative behavior or navigation
      console.log("View all bet slips clicked");
      // You could navigate to a different page or implement a different view
    },

    viewBetSlip(item) {
      // Implement alternative behavior or navigation
      console.log("View bet slip clicked for item:", item.bet_id);
      // You could navigate to a dedicated bet slip page
      // this.$router.push({ name: 'bet-slip-details', params: { id: item.bet_id } });
    },
    // Close bet slip carousel
    closeBetSlipCarousel() {
      this.showBetSlipCarousel = false;
    },
    // Enhanced carousel navigation methods
    prevSlide() {
      // Store the current index to apply 'prev' class
      const oldIndex = this.currentSlideIndex;
      
      if (this.currentSlideIndex > 0) {
        this.currentSlideIndex--;
      } else {
        // Loop to the last slide
        this.currentSlideIndex = this.carouselSlides.length - 1;
      }
      
      // Force DOM update to apply transition
      this.$nextTick(() => {
        // Add a class to slides that are moving out to the left
        document.querySelectorAll('.carousel-slide').forEach((el, index) => {
          if (index === oldIndex) {
            el.classList.add('prev');
            // Remove the class after animation completes
            setTimeout(() => {
              el.classList.remove('prev');
            }, 500);
          }
        });
      });
    },
    nextSlide() {
      // Store the current index to apply 'prev' class
      const oldIndex = this.currentSlideIndex;
      
      if (this.currentSlideIndex < this.carouselSlides.length - 1) {
        this.currentSlideIndex++;
      } else {
        // Loop to the first slide
        this.currentSlideIndex = 0;
      }
      
      // Force DOM update to apply transition
      this.$nextTick(() => {
        // Add a class to slides that are moving out to the left
        document.querySelectorAll('.carousel-slide').forEach((el, index) => {
          if (index === oldIndex) {
            el.classList.add('prev');
            // Remove the class after animation completes
            setTimeout(() => {
              el.classList.remove('prev');
            }, 500);
          }
        });
      });
    },
    goToSlide(index) {
      // Store the current index to apply 'prev' class
      const oldIndex = this.currentSlideIndex;
      
      // Only apply animation if actually changing slides
      if (oldIndex !== index) {
        this.currentSlideIndex = index;
        
        // Force DOM update to apply transition
        this.$nextTick(() => {
          // Add a class to slides that are moving out
          document.querySelectorAll('.carousel-slide').forEach((el, idx) => {
            if (idx === oldIndex) {
              el.classList.add('prev');
              // Remove the class after animation completes
              setTimeout(() => {
                el.classList.remove('prev');
              }, 500);
            }
          });
        });
      }
    }
  },
}
</script>

<style scoped>
/* Status badges */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.status-active {
  background-color: #2ecc71; /* Green */
}

.status-inactive {
  background-color: #e74c3c; /* Red */
}

.status-suspended {
  background-color: #f39c12; /* Orange */
}

.status-default {
  background-color: #95a5a6; /* Gray */
}

/* Bet type badges */
.bet-type-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 80px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  text-align: center;
}

/* Ensure dropdowns appear above the table */
.data-table {
  position: relative;
  z-index: 1;
}

/* Override any overflow settings that might be clipping the dropdown */
.absolute.top-0.bottom-0.left-0.right-0.overflow-auto {
  overflow: visible !important;
}

/* Make sure action dropdowns have the highest z-index */
:deep(.action-dropdown .absolute) {
  z-index: 9999 !important;
  position: fixed !important;
}

/* Enhanced carousel styles with sliding animations */
.carousel-container {
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.carousel-slide {
  flex: 0 0 100%;
  width: 100%;
}

/* Carousel navigation buttons */
.carousel-nav-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.carousel-nav-button:hover {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

/* Carousel indicators */
.carousel-indicator-dot {
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.carousel-indicator-dot:hover {
  transform: scale(1.2);
}

/* Transition for view switching with slide effect */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.5s ease-out;
}

.slide-fade-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}
</style>
