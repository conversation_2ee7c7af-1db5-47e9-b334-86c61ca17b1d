<template>
  <div class="relative z-0">

    <!-- Mobile Overlay -->
    <div v-if="isMobile && this.$store.state.isSideMenuOpen" class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
      @click="toggleSideM"></div>

    <div
      class="fixed left-0 top-0 bottom-0 bg-white rounded-r-sm shadow-lg overflow-auto z-50 transition-transform duration-300 ease-in-out"
      :class="[
        this.$store.state.isSideMenuOpen ? 'w-64' : 'w-16',
        isMobile && !this.$store.state.isSideMenuOpen ? '-translate-x-full' : 'translate-x-0'
      ]" style="background-color: #101624; color: white;">

      <div class="text-xl px-8 pt-6 flex" :class="{ 'px-4': !this.$store.state.isSideMenuOpen }">
        <img src="@/assets/mossbets-logo.png" alt="logo" />
        <small v-if="this.$store.state.isSideMenuOpen"><sup class="text-amber-600">BO</sup></small>
      </div>

      <div class="px-4 text-sm">


        <!-- Dashboard -->
        <div v-if="checkRole()" class="relative">
          <div class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
            :class="{ '': ['dashboard', 'summary-averages', 'games-summary'].includes(this.$route.name) }">
            <div class="w-full flex my-2 gap-4 items-center opacity-70 cursor-pointer relative"
              :class="{ 'opacity-100 font-bold text-accent': ['dashboard', 'summary-averages', 'games-summary'].includes(this.$route.name) }"
              @click="toggleDropdown('dashboard')">

              <!-- Dashboard Icon (Emoji) -->
              <div class="w-6 h-6 text-white text-xl flex items-center justify-center">
                📊
              </div>

              <span v-if="this.$store.state.isSideMenuOpen">Dashboard</span>

              <!-- Tooltip for collapsed mode -->
              <div v-if="!this.$store.state.isSideMenuOpen"
                class="absolute left-12 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
                Dashboard
              </div>

              <!-- spacing -->
              <div class="flex-grow"></div>

              <!-- Arrow for expanded mode -->
              <svg
                v-if="this.$store.state.isSideMenuOpen && (dropdownStates.dashboard || ['dashboard', 'summary-averages'].includes(this.$route.name))"
                class="w-4 h-4 text-white text-sm transition-transform"
                :class="{ 'rotate-180': dropdownStates.dashboard }" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
              </svg>
            </div>

            <!-- Nested System Links -->
            <div class="w-full overflow-hidden transition-all duration-300" :class="[
              this.$store.state.isSideMenuOpen ? 'pl-8' : 'pl-2',
              (dropdownStates.dashboard || ['dashboard', 'summary-averages', 'games-summary'].includes(this.$route.name)) ? 'max-h-60 pb-2' : 'max-h-0'
            ]">
              <div class="text-xs font-normal space-y-1">
                <router-link :to="{ name: 'dashboard' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['dashboard'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Summary</span>
                  <span v-else class="text-sm">📈</span>
                </router-link>
                <router-link :to="{ name: 'games-summary' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['games-summary'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Games Summary</span>
                  <span v-else class="text-sm">🎮</span>
                </router-link>

                <router-link :to="{ name: 'summary-averages' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['summary-averages'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Summary Average</span>
                  <span v-else class="text-sm">📊</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>


        <!-- Customer -->
        <div class="relative">
          <div class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
            :class="{ '': ['customer-search', 'customers', 'customers-old', 'customers-self-exclusion',].includes(this.$route.name) }">
            <div class="w-full flex my-2 gap-4 items-center opacity-70 cursor-pointer relative group"
              :class="{ 'opacity-100 font-bold text-accent': ['customer-search', 'customers', 'customers-old', 'customers-self-exclusion',].includes(this.$route.name) }"
              @click="toggleDropdown('customers')">

              <!-- Customer Icon (Emoji) -->
              <div class="w-6 h-6 text-white text-xl flex items-center justify-center">
                👥
              </div>

              <span v-if="this.$store.state.isSideMenuOpen">Customers</span>

              <!-- Tooltip for collapsed mode -->
              <div v-if="!this.$store.state.isSideMenuOpen"
                class="absolute left-12 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
                Customers
              </div>

              <!-- spacing -->
              <div class="flex-grow"></div>

              <!-- Arrow for expanded mode -->
              <svg
                v-if="this.$store.state.isSideMenuOpen && (dropdownStates.customers || ['customer-search', 'customers', 'customers-old', 'customers-self-exclusion'].includes(this.$route.name))"
                class="w-4 h-4 text-white text-sm transition-transform"
                :class="{ 'rotate-180': dropdownStates.customers }" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
              </svg>
            </div>

            <!-- Nested System Links -->
            <div class="w-full overflow-hidden transition-all duration-300" :class="[
              this.$store.state.isSideMenuOpen ? 'pl-8' : 'pl-2',
              (dropdownStates.customers || ['customer-search', 'customers', 'customers-old', 'customers-self-exclusion'].includes(this.$route.name)) ? 'max-h-40 pb-2' : 'max-h-0'
            ]">
              <div class="text-xs font-normal space-y-1">
                <router-link :to="{ name: 'customer-search' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['customer-search'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Search</span>
                  <span v-else class="text-sm">🔍</span>
                </router-link>

                <router-link :to="{ name: 'customers' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['customers'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Customers</span>
                  <span v-else class="text-sm">👤</span>
                </router-link>

                <router-link :to="{ name: 'customers-old' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['customers-old'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Customers Old</span>
                  <span v-else class="text-sm">👴</span>
                </router-link>

                <router-link :to="{ name: 'customers-self-exclusion' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['customers-self-exclusion'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Self Exclusion</span>
                  <span v-else class="text-sm">🚫</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>


        <!-- Transactions -->
        <div v-if="checkRole()" class="relative">
          <div class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
            :class="{ '': ['deposits', 'withdrawals', 'system-transactions', 'manual-reconciliation'].includes(this.$route.name) }">
            <div class="w-full flex my-2 gap-4 items-center opacity-70 cursor-pointer relative group"
              :class="{ 'opacity-100 font-bold text-accent': ['deposits', 'withdrawals', 'system-transactions', 'manual-reconciliation'].includes(this.$route.name) }"
              @click="toggleDropdown('transactions')">

              <!-- Transactions Icon (Emoji) -->
              <div class="w-6 h-6 text-white text-xl flex items-center justify-center">
                💰
              </div>

              <span v-if="this.$store.state.isSideMenuOpen">Transactions</span>

              <!-- Tooltip for collapsed mode -->
              <div v-if="!this.$store.state.isSideMenuOpen"
                class="absolute left-12 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
                Transactions
              </div>

              <!-- spacing -->
              <div class="flex-grow"></div>

              <!-- Arrow for expanded mode -->
              <svg
                v-if="this.$store.state.isSideMenuOpen && (dropdownStates.transactions || ['deposits', 'withdrawals', 'wallet-approvals', 'system-transactions', 'manual-reconciliation'].includes(this.$route.name))"
                class="w-4 h-4 text-white text-sm transition-transform"
                :class="{ 'rotate-180': dropdownStates.transactions }" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
              </svg>
            </div>

            <!-- Nested System Links -->
            <div class="w-full overflow-hidden transition-all duration-300" :class="[
              this.$store.state.isSideMenuOpen ? 'pl-8' : 'pl-2',
              (dropdownStates.transactions || ['deposits', 'withdrawals', 'wallet-approvals', 'system-transactions', 'manual-reconciliation'].includes(this.$route.name)) ? 'max-h-60 pb-2' : 'max-h-0'
            ]">
              <div class="text-xs font-normal space-y-1">
                <router-link :to="{ name: 'deposits' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['deposits'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Deposits</span>
                  <span v-else class="text-sm">⬇️</span>
                </router-link>

                <router-link :to="{ name: 'withdrawals' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['withdrawals'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Withdrawals</span>
                  <span v-else class="text-sm">⬆️</span>
                </router-link>

                <router-link v-if="checkRole()" :to="{ name: 'wallet-approvals' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['wallet-approvals'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Wallet Approvals</span>
                  <span v-else class="text-sm">✅</span>
                </router-link>

                <router-link :to="{ name: 'system-transactions' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['system-transactions'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">System Transactions</span>
                  <span v-else class="text-sm">⚙️</span>
                </router-link>

                <router-link v-if="checkRole()" :to="{ name: 'manual-reconciliation' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['manual-reconciliation'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Manual Reconciliation</span>
                  <span v-else class="text-sm">🔧</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>


        <!-- Sports Book -->
        <div v-if="checkRole()" class="relative">
          <div class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
            :class="{ '': ['fixtures', 'fixtures-manual-result', 'fixtures-manual-result-archive', 'odds-live', 'markets', 'tournaments'].includes(this.$route.name) }">
            <div class="w-full flex my-2 gap-4 items-center opacity-70 cursor-pointer relative group"
              :class="{ 'opacity-100 font-bold text-accent': ['fixtures', 'fixtures-manual-result', 'fixtures-manual-result-archive', 'odds-live', 'markets', 'tournaments'].includes(this.$route.name) }"
              @click="toggleDropdown('sportsbook')">

              <!-- Sports Book Icon (Emoji) -->
              <div class="w-6 h-6 text-white text-xl flex items-center justify-center">
                ⚽
              </div>

              <span v-if="this.$store.state.isSideMenuOpen">Sports Book</span>

              <!-- Tooltip for collapsed mode -->
              <div v-if="!this.$store.state.isSideMenuOpen"
                class="absolute left-12 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
                Sports Book
              </div>

              <!-- spacing -->
              <div class="flex-grow"></div>

              <!-- Arrow for expanded mode -->
              <svg
                v-if="this.$store.state.isSideMenuOpen && (dropdownStates.sportsbook || ['fixtures', 'fixtures-manual-result', 'fixtures-manual-result-archive', 'odds-live', 'markets', 'tournaments'].includes(this.$route.name))"
                class="w-4 h-4 text-white text-sm transition-transform"
                :class="{ 'rotate-180': dropdownStates.sportsbook }" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
              </svg>
            </div>

            <!-- Nested System Links -->
            <div class="w-full overflow-hidden transition-all duration-300" :class="[
              this.$store.state.isSideMenuOpen ? 'pl-8' : 'pl-2',
              (dropdownStates.sportsbook || ['fixtures', 'fixtures-manual-result', 'fixtures-manual-result-archive', 'odds-live', 'markets', 'tournaments'].includes(this.$route.name)) ? 'max-h-60 pb-2' : 'max-h-0'
            ]">
              <div class="text-xs font-normal space-y-1">
                <router-link :to="{ name: 'fixtures' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['fixtures'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Fixtures</span>
                  <span v-else class="text-sm">📅</span>
                </router-link>

                <router-link v-if="checkRole()" :to="{ name: 'fixtures-manual-result' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['fixtures-manual-result'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Manual Resulting</span>
                  <span v-else class="text-sm">✏️</span>
                </router-link>

                <router-link :to="{ name: 'fixtures-manual-result-archive' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['fixtures-manual-result-archive'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Manual Resulting Archive</span>
                  <span v-else class="text-sm">📁</span>
                </router-link>

                <router-link :to="{ name: 'markets' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['markets'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Markets</span>
                  <span v-else class="text-sm">🏪</span>
                </router-link>

                <router-link :to="{ name: 'tournaments' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['tournaments'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Tournaments</span>
                  <span v-else class="text-sm">🏆</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>


        <!-- Bets -->
        <div v-if="checkRole()" class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
          :class="{ '': ['sports-bets', 'virtual-bets', 'soft-gaming', 'casino-bets', 'bet-audits', 'bet-limits', 'bet-games', 'bets-games-categories', 'bet-ips', 'menu-highlights',].includes(this.$route.name) }">
          <!-- @click="toggleSideM"> -->
          <router-link :to="{ name: 'sports-bets' }" class="w-full flex my-2 gap-4 items-center opacity-70"
            :class="{ 'opacity-100 font-bold text-accent': ['sports-bets', 'virtual-bets', 'soft-gaming', 'casino-bets', 'bet-audits', 'bet-games', 'bets-games-categories', 'bet-ips', 'menu-highlights',].includes(this.$route.name) }">

            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <!-- Ticket body -->
              <path d="M5 8H19V18H5V8Z" fill="#f3f4f6" />
              <path d="M5 8H19V10.5L17.5 12L19 13.5V16H5V8Z" fill="#fff" />

              <!-- Dice symbols -->
              <path d="M8 11.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zM8 14.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"
                fill="#4b5563" />
              <path d="M13 11.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" fill="#4b5563" />
              <path d="M16 14.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" fill="#4b5563" />

              <!-- Chips stack -->
              <circle cx="18" cy="15" r="1.2" fill="#10b981" />
              <circle cx="16.5" cy="16.5" r="1.2" fill="#3b82f6" />
              <circle cx="18" cy="18" r="1.2" fill="#ef4444" />

              <!-- Arrow/Upward trend -->
              <path d="M9 6l3-3m0 0l3 3m-3-3v5" stroke="#22c55e" />
              <circle cx="12" cy="6" r="1.5" fill="#22c55e" />
            </svg>

            <span v-if="this.$store.state.isSideMenuOpen">Bets</span>
            <!-- spacing -->
            <div class="flex-grow"></div>
            <!-- Arrow Up SVG -->
            <svg
              v-if="['sports-bets', 'virtual-bets', 'soft-gaming', 'casino-bets', 'bet-audits', 'bet-limits', 'bet-games', 'bets-games-categories', 'bet-ips', 'menu-highlights',].includes(this.$route.name)"
              :class="{ 'opacity-100 font-bold text-accent': ['sports-bets', 'virtual-bets', 'soft-gaming', 'casino-bets', 'bet-audits', 'bet-limits', 'bet-games', 'bets-games-categories', 'bet-ips', 'menu-highlights',].includes(this.$route.name) }"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 8.25l-5.25 5.25m0-5.25L12 3m5.25 5.25L12 3m5.25 5.25L12 8.25" />
            </svg>
            <!-- Arrow Down SVG -->
            <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 15.75l5.25-5.25m0 5.25L12 21m-5.25-5.25L12 21m-5.25-5.25L12 15.75" />
            </svg>

          </router-link>

          <!-- Nested System Links with Indentation -->
          <div class="pt-1 pl-8 text-xs font-normal text-white w-full"
            v-show="['sports-bets', 'virtual-bets', 'soft-gaming', 'casino-bets', 'bet-audits', 'bet-limits', 'bet-games', 'bets-games-categories', 'bet-ips', 'menu-highlights',].includes(this.$route.name)">

            <!-- sports-bets -->
            <router-link :to="{ name: 'sports-bets' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['sports-bets'].includes(this.$route.name) }">
              Sports Bets
            </router-link>

            <!-- virtual-bets -->
            <router-link :to="{ name: 'virtual-bets' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['virtual-bets'].includes(this.$route.name) }">
              Virtual Bets
            </router-link>

            <!-- soft-gaming -->
            <router-link :to="{ name: 'soft-gaming' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['soft-gaming'].includes(this.$route.name) }">
              Soft Gaming
            </router-link>

            <!-- Casino Bets -->
            <router-link :to="{ name: 'casino-bets' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['casino-bets'].includes(this.$route.name) }">
              Casino Bets
            </router-link>

            <div class="border-b"></div>

            <!-- Bet Audits -->
            <router-link v-if="checkRole()" :to="{ name: 'bet-audits' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['bet-audits'].includes(this.$route.name) }">
              Bet Audits
            </router-link>

            <!-- Bet Limits -->
            <router-link v-if="checkRole()" :to="{ name: 'bet-limits' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['bet-limits'].includes(this.$route.name) }">
              Bet Limits
            </router-link>

            <!-- Bet Games -->
            <!-- <router-link v-if="checkRole()" :to="{ name: 'bet-games' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['bet-games'].includes(this.$route.name) }">
              Bet Games
            </router-link> -->

            <!-- Bets Games Categories -->
            <router-link v-if="checkRole()" :to="{ name: 'bets-games-categories' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['bets-games-categories'].includes(this.$route.name) }">
              Bet Games  & Categories
            </router-link>

            <!-- Bet IPs -->
            <router-link v-if="checkRole()" :to="{ name: 'bet-ips' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['bet-ips'].includes(this.$route.name) }">
              Bet IPs
            </router-link>

            <div class="border-b"></div>

            <!-- Menu Highlights -->
            <router-link v-if="checkRole()" :to="{ name: 'menu-highlights' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['menu-highlights'].includes(this.$route.name) }">
              Menu Highlights
            </router-link>

          </div>
        </div>


        <!-- Promotions Menu -->
        <div v-if="checkRole()" class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
          :class="{ '': ['promotions', 'promotions-add', 'promotions-edit', 'campaigns',].includes(this.$route.name) }">
          <router-link :to="{ name: 'promotions' }" class="w-full flex my-2 gap-4 items-center opacity-70"
            :class="{ 'opacity-100 font-bold text-accent': ['promotions', 'promotions-add', 'promotions-edit', 'campaigns', 'leaderboard',].includes(this.$route.name) }">

            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12 2C11.45 2 10.96 2.18 10.59 2.54L3.54 9.59C2.96 10.16 2.96 11.08 3.54 11.66L11.66 19.78C12.24 20.36 13.16 20.36 13.74 19.78L20.79 12.73C21.37 12.16 21.37 11.24 20.79 10.66L13.41 3.29C13.04 2.92 12.55 2.74 12 2ZM12 7.5C13.1 7.5 14 8.4 14 9.5C14 10.6 13.1 11.5 12 11.5C10.9 11.5 10 10.6 10 9.5C10 8.4 10.9 7.5 12 7.5ZM5 16.59L6.41 15.17L11 19.76L9.59 21.17L5 16.59Z" />
            </svg>

            <span v-if="this.$store.state.isSideMenuOpen">Promotions</span>
            <!-- spacing -->
            <div class="flex-grow"></div>
            <!-- Arrow Up SVG -->
            <svg
              v-if="['promotions', 'promotions-add', 'promotions-edit', 'campaigns', 'leaderboard',].includes(this.$route.name)"
              :class="{ 'opacity-100 font-bold text-accent': ['promotions', 'promotions-add', 'promotions-edit', 'campaigns', 'leaderboard',].includes(this.$route.name) }"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 8.25l-5.25 5.25m0-5.25L12 3m5.25 5.25L12 3m5.25 5.25L12 8.25" />
            </svg>
            <!-- Arrow Down SVG -->
            <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 15.75l5.25-5.25m0 5.25L12 21m-5.25-5.25L12 21m-5.25-5.25L12 15.75" />
            </svg>

          </router-link>

          <!-- Nested System Links with Indentation -->
          <div class="pt-1 pl-8 text-xs font-normal text-white w-full"
            v-show="['promotions', 'promotions-add', 'promotions-edit', 'campaigns', 'campaigns-add', 'campaigns-edit', 'leaderboard',].includes(this.$route.name)">

            <!-- Promotions -->
            <router-link :to="{ name: 'promotions' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['promotions'].includes(this.$route.name) }">
              Promotions
            </router-link>

            <!-- Campaigns  -->
            <router-link :to="{ name: 'campaigns' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['campaigns'].includes(this.$route.name) }">
              Campaigns
            </router-link>

            <!-- Leaderboard -->
            <router-link :to="{ name: 'leaderboard' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['leaderboard'].includes(this.$route.name) }">
              Leaderboard
            </router-link>

          </div>
        </div>


        <!-- Taxes -->
        <div v-if="checkRole()" class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
          :class="{ '': ['taxes', 'tax-summary', 'tax-payments'].includes(this.$route.name) }">
          <router-link :to="{ name: 'taxes' }" class="w-full flex my-2 gap-4 items-center opacity-70"
            :class="{ 'opacity-100 font-bold text-accent': ['taxes', 'tax-summary', 'tax-payments'].includes(this.$route.name) }">

            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="2" y="4" width="20" height="16" rx="2" ry="2" fill="#f2f2f2" />
              <line x1="6" y1="8" x2="18" y2="8" stroke="#000" />
              <line x1="6" y1="12" x2="10" y2="12" stroke="#000" />
              <line x1="6" y1="16" x2="8" y2="16" stroke="#000" />
              <circle cx="16" cy="14" r="2" fill="#000" />
              <path d="M14 10l4 4" stroke="#000" />
              <path d="M18 10l-4 4" stroke="#000" />
            </svg>

            <span v-if="this.$store.state.isSideMenuOpen">Taxes</span>
            <!-- spacing -->
            <div class="flex-grow"></div>
            <!-- Arrow Up SVG -->
            <svg v-if="['taxes', 'tax-summary', 'tax-payments'].includes(this.$route.name)"
              :class="{ 'opacity-100 font-bold text-accent': ['taxes', 'tax-summary', 'tax-payments'].includes(this.$route.name) }"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 8.25l-5.25 5.25m0-5.25L12 3m5.25 5.25L12 3m5.25 5.25L12 8.25" />
            </svg>
            <!-- Arrow Down SVG -->
            <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 15.75l5.25-5.25m0 5.25L12 21m-5.25-5.25L12 21m-5.25-5.25L12 15.75" />
            </svg>

          </router-link>

          <!-- Nested System Links with Indentation -->
          <div class="pt-1 pl-8 text-xs font-normal text-white w-full"
            v-show="['taxes', 'tax-summary', 'tax-payments'].includes(this.$route.name)">

            <!-- Taxes -->
            <router-link :to="{ name: 'taxes' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['taxes'].includes(this.$route.name) }">
              Taxes
            </router-link>

            <!-- Tax Summary -->
            <router-link :to="{ name: 'tax-summary' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['tax-summary'].includes(this.$route.name) }">
              Tax Summary
            </router-link>

            <!-- Tax Payments -->
            <router-link :to="{ name: 'tax-payments' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['tax-payments'].includes(this.$route.name) }">
              Tax Payments
            </router-link>

          </div>
        </div>


        <!-- SMS -->
        <div v-if="checkRole()" class="relative">
          <div class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
            :class="{ '': ['bulk-sms', 'outbox', 'scheduled-sms'].includes(this.$route.name) }">
            <div class="w-full flex my-2 gap-4 items-center opacity-70 cursor-pointer relative group"
              :class="{ 'opacity-100 font-bold text-accent': ['bulk-sms', 'outbox', 'scheduled-sms'].includes(this.$route.name) }"
              @click="toggleDropdown('sms')">

              <!-- SMS Icon (Emoji) -->
              <div class="w-6 h-6 text-white text-xl flex items-center justify-center">
                💬
              </div>

              <span v-if="this.$store.state.isSideMenuOpen">SMS</span>

              <!-- Tooltip for collapsed mode -->
              <div v-if="!this.$store.state.isSideMenuOpen"
                class="absolute left-12 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
                SMS
              </div>

              <!-- spacing -->
              <div class="flex-grow"></div>

              <!-- Arrow for expanded mode -->
              <svg
                v-if="this.$store.state.isSideMenuOpen && (dropdownStates.sms || ['bulk-sms', 'outbox', 'scheduled-sms'].includes(this.$route.name))"
                class="w-4 h-4 text-white text-sm transition-transform" :class="{ 'rotate-180': dropdownStates.sms }"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
              </svg>
            </div>

            <!-- Nested System Links -->
            <div class="w-full overflow-hidden transition-all duration-300" :class="[
              this.$store.state.isSideMenuOpen ? 'pl-8' : 'pl-2',
              (dropdownStates.sms || ['bulk-sms', 'outbox', 'scheduled-sms'].includes(this.$route.name)) ? 'max-h-60 pb-2' : 'max-h-0'
            ]">
              <div class="text-xs font-normal space-y-1">
                <router-link :to="{ name: 'bulk-sms' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['bulk-sms'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">BulkSMS</span>
                  <span v-else class="text-sm">📤</span>
                </router-link>

                <router-link :to="{ name: 'outbox' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['outbox'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">OutBox</span>
                  <span v-else class="text-sm">📥</span>
                </router-link>

                <router-link :to="{ name: 'scheduled-sms' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['scheduled-sms'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Scheduled</span>
                  <span v-else class="text-sm">📅</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>




        <!-- Reports -->
        <div class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full"
          :class="{ '': ['special-reports'].includes(this.$route.name) }">
          <router-link :to="{ name: 'special-reports' }" class="w-full flex my-2 gap-4 items-center opacity-70"
            :class="{ 'opacity-100 font-bold text-accent': ['special-reports'].includes(this.$route.name) }">

            <!-- Reports Icon (Emoji) -->
            <div class="w-6 h-6 text-white text-xl flex items-center justify-center">
              📊
            </div>

            <span v-if="this.$store.state.isSideMenuOpen">Reports</span>

            <!-- spacing -->
            <div class="flex-grow"></div>

            <!-- Arrow Up SVG -->
            <svg v-if="['special-reports'].includes(this.$route.name)"
              :class="{ 'opacity-100 font-bold text-accent': ['special-reports'].includes(this.$route.name) }"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 8.25l-5.25 5.25m0-5.25L12 3m5.25 5.25L12 3m5.25 5.25L12 8.25" />
            </svg>
            <!-- Arrow Down SVG -->
            <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="w-4 h-4 text-white text-sm">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12 15.75l5.25-5.25m0 5.25L12 21m-5.25-5.25L12 21m-5.25-5.25L12 15.75" />
            </svg>

          </router-link>

          <!-- Nested System Links with Indentation -->
          <div class="pt-1 pl-8 text-xs font-normal text-white w-full"
            v-show="['special-reports'].includes(this.$route.name)">

            <!-- Special Reports -->
            <router-link :to="{ name: 'special-reports' }" class="block w-full my-1 opacity-70"
              :class="{ 'font-medium text-accent opacity-100': ['special-reports'].includes(this.$route.name) }">
              Special Reports
            </router-link>

          </div>
        </div>

        <!-- Pragmatic -->
        <div v-if="checkRole()" class="relative">
          <router-link :to="{ name: 'pragmatic' }" class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full" :class="{
            'bg-accent text-white': ['pragmatic'].includes(this.$route.name)
          }">
            <div class="w-full flex my-2 gap-4 items-center opacity-70 cursor-pointer relative group" :class="{
              'opacity-100 font-bold text-white': ['pragmatic'].includes(this.$route.name)
            }">
              <!-- Icon -->
              <div class="text-lg">🎰</div>

              <!-- Text for expanded mode -->
              <span v-if="this.$store.state.isSideMenuOpen" class="text-sm font-medium">Pragmatic</span>

              <!-- Tooltip for collapsed mode -->
              <div v-if="!this.$store.state.isSideMenuOpen"
                class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity z-50 whitespace-nowrap">
                Pragmatic
              </div>
            </div>
          </router-link>
        </div>

        <!-- System -->
        <div v-if="checkRole()" class="relative">
          <div class="flex flex-col items-start rounded-lg shadow-lg my-2 px-2 border w-full" :class="{
            '': [
              'system-users', 'system-users-add', 'system-users-edit',
              'roles', 'roles-add', 'roles-edit',
              'permissions', 'permissions-add', 'permissions-edit',
              'paybills', 'paybills-add', 'paybills-edit',
              'auth-channels', 'auth-channels-add', 'auth-channels-edit',
            ].includes(this.$route.name)
          }">
            <div class="w-full flex my-2 gap-4 items-center opacity-70 cursor-pointer relative group" :class="{
              'opacity-100 font-bold text-accent':
                [
                  'system-users', 'system-users-add', 'system-users-edit',
                  'roles', 'roles-add', 'roles-edit',
                  'permissions', 'permissions-add', 'permissions-edit',
                  'paybills', 'paybills-add', 'paybills-edit',
                  'auth-channels', 'auth-channels-add', 'auth-channels-edit',
                ].includes(this.$route.name)
            }" @click="toggleDropdown('system')">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="w-6 h-6">
                <!-- PC Desktop -->
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M8 20h8M9 16h6m-1.5 2h-3" />

                <!-- Cog (Gear) -->
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M12 6V4m0 16v-2m4.24-11.66l1.42-1.42M4.34 19.66l1.42-1.42M18 12h2M4 12h2m11.66 4.24l1.42 1.42M4.34 4.34l1.42 1.42M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>

              <span v-if="this.$store.state.isSideMenuOpen">System</span>

              <!-- Tooltip for collapsed mode -->
              <div v-if="!this.$store.state.isSideMenuOpen"
                class="absolute left-12 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity z-50 pointer-events-none">
                System
              </div>

              <!-- spacing -->
              <div class="flex-grow"></div>

              <!-- Arrow for expanded mode -->
              <svg
                v-if="this.$store.state.isSideMenuOpen && (dropdownStates.system || ['system-users', 'system-users-add', 'system-users-edit', 'roles', 'roles-add', 'roles-edit', 'permissions', 'permissions-add', 'permissions-edit', 'paybills', 'paybills-add', 'paybills-edit', 'auth-channels', 'auth-channels-add', 'auth-channels-edit'].includes(this.$route.name))"
                class="w-4 h-4 text-white text-sm transition-transform" :class="{ 'rotate-180': dropdownStates.system }"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
              </svg>
            </div>

            <!-- Nested System Links -->
            <div class="w-full overflow-hidden transition-all duration-300" :class="[
              this.$store.state.isSideMenuOpen ? 'pl-8' : 'pl-2',
              (dropdownStates.system || ['system-users', 'system-users-add', 'system-users-edit', 'roles', 'roles-add', 'roles-edit', 'permissions', 'permissions-add', 'permissions-edit', 'paybills', 'paybills-add', 'paybills-edit', 'auth-channels', 'auth-channels-add', 'auth-channels-edit'].includes(this.$route.name)) ? 'max-h-60 pb-2' : 'max-h-0'
            ]">
              <div class="text-xs font-normal space-y-1">
                <router-link :to="{ name: 'system-users' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['system-users', 'system-users-add', 'system-users-edit'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Users</span>
                  <span v-else class="text-sm">👥</span>
                </router-link>

                <router-link :to="{ name: 'roles' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['roles', 'roles-add', 'roles-edit'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Roles</span>
                  <span v-else class="text-sm">🎭</span>
                </router-link>

                <router-link :to="{ name: 'permissions' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['permissions'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Permissions</span>
                  <span v-else class="text-sm">🔐</span>
                </router-link>

                <router-link v-if="isSuperAdmin()" :to="{ name: 'paybills' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['paybills', 'paybills-add', 'paybills-edit'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">PayBills</span>
                  <span v-else class="text-sm">💳</span>
                </router-link>

                <router-link v-if="isSuperAdmin()" :to="{ name: 'auth-channels' }"
                  class="block w-full py-1 opacity-70 hover:opacity-100 transition-opacity"
                  :class="{ 'font-medium text-accent opacity-100': ['auth-channels', 'auth-channels-add', 'auth-channels-edit'].includes(this.$route.name) }">
                  <span v-if="this.$store.state.isSideMenuOpen">Auth Channels</span>
                  <span v-else class="text-sm">🔗</span>
                </router-link>
              </div>
            </div>
          </div>
        </div>


        <div class="pt-4"></div>
        <div v-if="!checkRole()" class="pt-32"> </div>

        <button style="background-color: coral;"
          class="flex  w-full mt-20 px-4 py-2 rounded-md text-white bg-coral-500 hover:bg-coral-600 focus:ring-2 focus:ring-coral-600 focus:ring-opacity-50"
          @click="confirmLogOut">

          <span class="align-middle">Logout</span>
          <!-- spacing -->
          <div class="flex-grow"></div>

          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="w-6 h-6 inline-block align-middle -mt-1 mr-0">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
          </svg>
        </button>

      </div>
    </div>

    <div class="main-content fixed top-0 bottom-0 right-0 overflow-auto transition-all duration-300 ease-in-out" :style="{
      left: isMobile ? '0' : (this.$store.state.isSideMenuOpen ? '16rem' : '4rem'),
      marginLeft: isMobile ? '0' : '0'
    }" :class="[
      isMobile ? 'w-full' : '',
      isMobile && this.$store.state.isSideMenuOpen ? 'pointer-events-none' : ''
    ]">
      <div class="w-full h-full">
        <slot></slot>
      </div>
    </div>

  </div>
</template>

<script>
import { mapActions } from "vuex";

export default {
  data() {
    return {
      authorized: false,
      windowWidth: window.innerWidth,
      dropdownStates: {
        dashboard: false,
        customers: false,
        transactions: false,
        sportsbook: false,
        bets: false,
        promotions: false,
        taxes: false,
        sms: false,
        system: false,
      }
    }
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768;
    },
    isTablet() {
      return this.windowWidth > 768 && this.windowWidth <= 1024;
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
    // Set initial dropdown state based on current route
    this.updateDropdownFromRoute();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    // Watch for route changes and update dropdown states accordingly
    '$route'() {
      this.updateDropdownFromRoute();
    }
  },
  methods: {
    ...mapActions(["LogOut", "toggleSideMenu",]),
    handleResize() {
      this.windowWidth = window.innerWidth;
      // Auto-close sidebar on mobile when resizing to mobile view
      if (this.isMobile && this.$store.state.isSideMenuOpen) {
        this.toggleSideMenu();
      }
    },
    toggleSideM() {
      this.toggleSideMenu();
    },
    toggleDropdown(section) {
      // Define the default/first page for each section
      const defaultRoutes = {
        dashboard: 'dashboard',
        customers: 'customer-search',
        transactions: 'deposits',
        sportsbook: 'fixtures',
        sms: 'bulk-sms',
        system: 'system-users'
      };

      // If sidebar is collapsed, expand it first
      if (!this.$store.state.isSideMenuOpen) {
        this.toggleSideMenu();
        // Small delay to allow sidebar to expand before opening dropdown
        setTimeout(() => {
          this.closeAllDropdowns();
          this.dropdownStates[section] = true;
          // Navigate to the first page of the section
          if (defaultRoutes[section]) {
            this.$router.push({ name: defaultRoutes[section] });
          }
        }, 100);
      } else {
        // Close all other dropdowns first
        const wasOpen = this.dropdownStates[section];
        this.closeAllDropdowns();

        // If dropdown wasn't open, open it and navigate to first page
        if (!wasOpen) {
          this.dropdownStates[section] = true;
          // Navigate to the first page of the section
          if (defaultRoutes[section]) {
            this.$router.push({ name: defaultRoutes[section] });
          }
        }
        // If it was open, keep it closed (already closed by closeAllDropdowns)
      }
    },

    closeAllDropdowns() {
      // Close all dropdowns
      Object.keys(this.dropdownStates).forEach(key => {
        this.dropdownStates[key] = false;
      });
    },

    updateDropdownFromRoute() {
      // Close all dropdowns first
      this.closeAllDropdowns();

      // Open the appropriate dropdown based on current route
      const routeName = this.$route.name;

      // Dashboard routes
      if (['dashboard', 'summary-averages', 'games-summary'].includes(routeName)) {
        this.dropdownStates.dashboard = true;
      }
      // Customer routes
      else if (['customer-search', 'customers', 'customers-old', 'customers-self-exclusion'].includes(routeName)) {
        this.dropdownStates.customers = true;
      }
      // Transaction routes
      else if (['deposits', 'withdrawals', 'wallet-approvals', 'system-transactions', 'manual-reconciliation'].includes(routeName)) {
        this.dropdownStates.transactions = true;
      }
      // Sportsbook routes
      else if (['fixtures', 'fixtures-manual-result', 'fixtures-manual-result-archive', 'odds-live', 'markets', 'tournaments'].includes(routeName)) {
        this.dropdownStates.sportsbook = true;
      }
      // SMS routes
      else if (['bulk-sms', 'outbox', 'scheduled-sms'].includes(routeName)) {
        this.dropdownStates.sms = true;
      }
      // Reports routes - no longer a dropdown, handled like Taxes/Bets/Promotions
      // System routes (if you have any dropdown system routes)
      else if (['system-users', 'system-users-add', 'system-users-edit', 'roles', 'roles-add', 'roles-edit', 'permissions', 'permissions-add', 'permissions-edit', 'paybills', 'paybills-add', 'paybills-edit', 'auth-channels', 'auth-channels-add', 'auth-channels-edit'].includes(routeName)) {
        this.dropdownStates.system = true;
      }
    },


    //
    //
    checkHasPermissions(permission) {
      return this.$store.state.permissions.includes(permission)
    },

    //
    checkRole() {
      return this.$store.state.role === '1' || this.$store.state.role === '2'
    },

    isSuperAdmin() {
      return this.$store.state.role === '1'
    },

    async confirmLogOut() {
      let app = this

      app.$swal.fire({
        title: 'Logout',
        text: "Are you sure you want to logout?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.logout()
        },
      })
        .then(async (result) => {

        })
    },


    async logout() {
      await this.LogOut();
    },

    async getToken() {
      let app = this

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this generates a new token!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, generate!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.generateToken({})
        },
      })
        .then(async (result) => {
          if (result.value.status === 200) {
            app.$swal.fire('Generated!', result.value.message, 'success')
          } else {
            app.$swal.fire('Error!', result.value.message, 'error')
          }
        })
    },

    getPermission(name) {
      //let app = this
      if (this.$store.state.role === 1 && name !== 'Read Kiron Statistics') {
        return true
      }
      // kiron
      if (name === 'Read Kiron Statistics' && this.$store.state.role === 8) {
        return true
      }
      let permissions = this.$store.state.permission
      for (let item of permissions) {
        if (item.name === name) {
          //console.log('returning true: ' + name)
          return true
        }
      }
      //console.log('returning false: ' + name)
      return false

    }
  }
}
</script>
